/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/products/page";
exports.ids = ["app/products/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/page.tsx */ \"(rsc)/./src/app/products/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'products',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/products/page\",\n        pathname: \"/products\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VtZXIlMjBGYXJvb3ElNUMlNUNEZXNrdG9wJTVDJTVDUGF0cmlja3MlMjB3ZWIlNUMlNUNDYXN0LVN0b25lJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVbWVyJTIwRmFyb29xJTVDJTVDRGVza3RvcCU1QyU1Q1BhdHJpY2tzJTIwd2ViJTVDJTVDQ2FzdC1TdG9uZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVW1lciUyMEZhcm9vcSU1QyU1Q0Rlc2t0b3AlNUMlNUNQYXRyaWNrcyUyMHdlYiU1QyU1Q0Nhc3QtU3RvbmUlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VtZXIlMjBGYXJvb3ElNUMlNUNEZXNrdG9wJTVDJTVDUGF0cmlja3MlMjB3ZWIlNUMlNUNDYXN0LVN0b25lJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VtZXIlMjBGYXJvb3ElNUMlNUNEZXNrdG9wJTVDJTVDUGF0cmlja3MlMjB3ZWIlNUMlNUNDYXN0LVN0b25lJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VtZXIlMjBGYXJvb3ElNUMlNUNEZXNrdG9wJTVDJTVDUGF0cmlja3MlMjB3ZWIlNUMlNUNDYXN0LVN0b25lJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VtZXIlMjBGYXJvb3ElNUMlNUNEZXNrdG9wJTVDJTVDUGF0cmlja3MlMjB3ZWIlNUMlNUNDYXN0LVN0b25lJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VtZXIlMjBGYXJvb3ElNUMlNUNEZXNrdG9wJTVDJTVDUGF0cmlja3MlMjB3ZWIlNUMlNUNDYXN0LVN0b25lJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBc0s7QUFDdEs7QUFDQSwwT0FBeUs7QUFDeks7QUFDQSwwT0FBeUs7QUFDeks7QUFDQSxvUkFBK0w7QUFDL0w7QUFDQSx3T0FBd0s7QUFDeEs7QUFDQSw0UEFBbUw7QUFDbkw7QUFDQSxrUUFBc0w7QUFDdEw7QUFDQSxzUUFBdUwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVtZXIgRmFyb29xXFxcXERlc2t0b3BcXFxcUGF0cmlja3Mgd2ViXFxcXENhc3QtU3RvbmVcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVW1lciBGYXJvb3FcXFxcRGVza3RvcFxcXFxQYXRyaWNrcyB3ZWJcXFxcQ2FzdC1TdG9uZVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVbWVyIEZhcm9vcVxcXFxEZXNrdG9wXFxcXFBhdHJpY2tzIHdlYlxcXFxDYXN0LVN0b25lXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVtZXIgRmFyb29xXFxcXERlc2t0b3BcXFxcUGF0cmlja3Mgd2ViXFxcXENhc3QtU3RvbmVcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVW1lciBGYXJvb3FcXFxcRGVza3RvcFxcXFxQYXRyaWNrcyB3ZWJcXFxcQ2FzdC1TdG9uZVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVtZXIgRmFyb29xXFxcXERlc2t0b3BcXFxcUGF0cmlja3Mgd2ViXFxcXENhc3QtU3RvbmVcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVW1lciBGYXJvb3FcXFxcRGVza3RvcFxcXFxQYXRyaWNrcyB3ZWJcXFxcQ2FzdC1TdG9uZVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVbWVyIEZhcm9vcVxcXFxEZXNrdG9wXFxcXFBhdHJpY2tzIHdlYlxcXFxDYXN0LVN0b25lXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../node_modules/react-hot-toast/dist/index.mjs */ \"(rsc)/../node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/page.tsx */ \"(rsc)/./src/app/products/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VtZXIlMjBGYXJvb3ElNUMlNUNEZXNrdG9wJTVDJTVDUGF0cmlja3MlMjB3ZWIlNUMlNUNDYXN0LVN0b25lJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm9kdWN0cyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBc0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVtZXIgRmFyb29xXFxcXERlc2t0b3BcXFxcUGF0cmlja3Mgd2ViXFxcXENhc3QtU3RvbmVcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwcm9kdWN0c1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXFBhdHJpY2tzIHdlYlxcQ2FzdC1TdG9uZVxcZnJvbnRlbmRcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1643d5daa2d7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxNjQzZDVkYWEyZDdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"Cast Stone Interiors & Decorations - Timeless Elegance\",\n    description: \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\",\n    keywords: \"cast stone, architectural elements, fireplaces, decorative pieces, interior design, handcrafted stone\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 3000,\n                        style: {\n                            background: 'var(--cart-bg)',\n                            color: 'var(--cart-text-primary)',\n                            border: '1px solid var(--cart-border)',\n                            borderRadius: '8px',\n                            boxShadow: '0 4px 12px var(--cart-shadow)'\n                        },\n                        success: {\n                            iconTheme: {\n                                primary: 'var(--cart-success)',\n                                secondary: 'white'\n                            }\n                        },\n                        error: {\n                            iconTheme: {\n                                primary: 'var(--cart-error)',\n                                secondary: 'white'\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFLTUE7QUFLQUM7QUFSb0M7QUFDbkI7QUFZaEIsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0FBQ1osRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdaLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDOztnQkFFbkVPOzhCQUNELDhEQUFDTixvREFBT0E7b0JBQ05ZLFVBQVM7b0JBQ1RDLGNBQWM7d0JBQ1pDLFVBQVU7d0JBQ1ZDLE9BQU87NEJBQ0xDLFlBQVk7NEJBQ1pDLE9BQU87NEJBQ1BDLFFBQVE7NEJBQ1JDLGNBQWM7NEJBQ2RDLFdBQVc7d0JBQ2I7d0JBQ0FDLFNBQVM7NEJBQ1BDLFdBQVc7Z0NBQ1RDLFNBQVM7Z0NBQ1RDLFdBQVc7NEJBQ2I7d0JBQ0Y7d0JBQ0FDLE9BQU87NEJBQ0xILFdBQVc7Z0NBQ1RDLFNBQVM7Z0NBQ1RDLFdBQVc7NEJBQ2I7d0JBQ0Y7b0JBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXFBhdHJpY2tzIHdlYlxcQ2FzdC1TdG9uZVxcZnJvbnRlbmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xyXG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XHJcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xyXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XHJcblxyXG5jb25zdCBnZWlzdFNhbnMgPSBHZWlzdCh7XHJcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcclxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcclxufSk7XHJcblxyXG5jb25zdCBnZWlzdE1vbm8gPSBHZWlzdF9Nb25vKHtcclxuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFxyXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxyXG59KTtcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6IFwiQ2FzdCBTdG9uZSBJbnRlcmlvcnMgJiBEZWNvcmF0aW9ucyAtIFRpbWVsZXNzIEVsZWdhbmNlXCIsXHJcbiAgZGVzY3JpcHRpb246IFwiRGlzY292ZXIgb3VyIGV4cXVpc2l0ZSBjb2xsZWN0aW9uIG9mIGhhbmRjcmFmdGVkIGNhc3Qgc3RvbmUgaW50ZXJpb3JzLCBmaXJlcGxhY2VzLCBhbmQgZGVjb3JhdGl2ZSBlbGVtZW50cyB0aGF0IHRyYW5zZm9ybSBzcGFjZXMgaW50byB3b3JrcyBvZiBhcnQuXCIsXHJcbiAga2V5d29yZHM6IFwiY2FzdCBzdG9uZSwgYXJjaGl0ZWN0dXJhbCBlbGVtZW50cywgZmlyZXBsYWNlcywgZGVjb3JhdGl2ZSBwaWVjZXMsIGludGVyaW9yIGRlc2lnbiwgaGFuZGNyYWZ0ZWQgc3RvbmVcIixcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xyXG4gIGNoaWxkcmVuLFxyXG59OiBSZWFkb25seTx7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufT4pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgIDxib2R5XHJcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XHJcbiAgICAgID5cclxuICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgPFRvYXN0ZXJcclxuICAgICAgICAgIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCJcclxuICAgICAgICAgIHRvYXN0T3B0aW9ucz17e1xyXG4gICAgICAgICAgICBkdXJhdGlvbjogMzAwMCxcclxuICAgICAgICAgICAgc3R5bGU6IHtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAndmFyKC0tY2FydC1iZyknLFxyXG4gICAgICAgICAgICAgIGNvbG9yOiAndmFyKC0tY2FydC10ZXh0LXByaW1hcnkpJyxcclxuICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgdmFyKC0tY2FydC1ib3JkZXIpJyxcclxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxyXG4gICAgICAgICAgICAgIGJveFNoYWRvdzogJzAgNHB4IDEycHggdmFyKC0tY2FydC1zaGFkb3cpJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc3VjY2Vzczoge1xyXG4gICAgICAgICAgICAgIGljb25UaGVtZToge1xyXG4gICAgICAgICAgICAgICAgcHJpbWFyeTogJ3ZhcigtLWNhcnQtc3VjY2VzcyknLFxyXG4gICAgICAgICAgICAgICAgc2Vjb25kYXJ5OiAnd2hpdGUnLFxyXG4gICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGVycm9yOiB7XHJcbiAgICAgICAgICAgICAgaWNvblRoZW1lOiB7XHJcbiAgICAgICAgICAgICAgICBwcmltYXJ5OiAndmFyKC0tY2FydC1lcnJvciknLFxyXG4gICAgICAgICAgICAgICAgc2Vjb25kYXJ5OiAnd2hpdGUnLFxyXG4gICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJUb2FzdGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSIsInBvc2l0aW9uIiwidG9hc3RPcHRpb25zIiwiZHVyYXRpb24iLCJzdHlsZSIsImJhY2tncm91bmQiLCJjb2xvciIsImJvcmRlciIsImJvcmRlclJhZGl1cyIsImJveFNoYWRvdyIsInN1Y2Nlc3MiLCJpY29uVGhlbWUiLCJwcmltYXJ5Iiwic2Vjb25kYXJ5IiwiZXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\products\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/../node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/page.tsx */ \"(ssr)/./src/app/products/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VtZXIlMjBGYXJvb3ElNUMlNUNEZXNrdG9wJTVDJTVDUGF0cmlja3MlMjB3ZWIlNUMlNUNDYXN0LVN0b25lJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm9kdWN0cyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBc0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVtZXIgRmFyb29xXFxcXERlc2t0b3BcXFxcUGF0cmlja3Mgd2ViXFxcXENhc3QtU3RvbmVcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwcm9kdWN0c1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/products/page.module.css":
/*!******************************************!*\
  !*** ./src/app/products/page.module.css ***!
  \******************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"page_container__MNY9B\",\n\t\"navigation\": \"page_navigation__2pRTH\",\n\t\"navContainer\": \"page_navContainer__92Oxa\",\n\t\"logo\": \"page_logo__CkXj_\",\n\t\"navMenu\": \"page_navMenu__ICbru\",\n\t\"active\": \"page_active__ueGNe\",\n\t\"hero\": \"page_hero__M_w3l\",\n\t\"heroContent\": \"page_heroContent__8l0X0\",\n\t\"heroTitle\": \"page_heroTitle__u3dCo\",\n\t\"heroSubtitle\": \"page_heroSubtitle__zMuar\",\n\t\"productsSection\": \"page_productsSection__p8BO0\",\n\t\"categoryFilter\": \"page_categoryFilter__YbRKg\",\n\t\"categoryBtn\": \"page_categoryBtn__y6TMw\",\n\t\"productsGrid\": \"page_productsGrid__koizS\",\n\t\"productCard\": \"page_productCard__TcT5K\",\n\t\"productImageContainer\": \"page_productImageContainer__TOOvX\",\n\t\"productImage\": \"page_productImage__Riyzl\",\n\t\"productOverlay\": \"page_productOverlay__0_8Mg\",\n\t\"viewBtn\": \"page_viewBtn__LB3A1\",\n\t\"cartBtn\": \"page_cartBtn__Hp2sn\",\n\t\"productInfo\": \"page_productInfo__3g6zS\",\n\t\"productName\": \"page_productName__S8OlF\",\n\t\"productDescription\": \"page_productDescription__20458\",\n\t\"productFooter\": \"page_productFooter__GIYw7\",\n\t\"productPrice\": \"page_productPrice__GQ1oR\",\n\t\"inquireBtn\": \"page_inquireBtn__ADCWS\",\n\t\"ctaSection\": \"page_ctaSection___VqAL\",\n\t\"ctaContent\": \"page_ctaContent___JlH_\",\n\t\"ctaBtn\": \"page_ctaBtn__q7_TQ\",\n\t\"footer\": \"page_footer__4_tZ4\",\n\t\"footerContent\": \"page_footerContent__J_rzp\",\n\t\"footerSection\": \"page_footerSection__8hhlT\",\n\t\"footerBottom\": \"page_footerBottom__427u6\",\n\t\"mobileMenuToggle\": \"page_mobileMenuToggle__AI8HH\",\n\t\"hamburgerLine\": \"page_hamburgerLine__GorP2\",\n\t\"mobileMenu\": \"page_mobileMenu__rjGsA\",\n\t\"mobileNavMenu\": \"page_mobileNavMenu__GsbyN\",\n\t\"mobileDropdownToggle\": \"page_mobileDropdownToggle___rg49\",\n\t\"mobileDropdownMenu\": \"page_mobileDropdownMenu__7w_07\"\n};\n\nmodule.exports.__checksum = \"11ff29d711bc\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb2R1Y3RzL3BhZ2UubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGFwcFxccHJvZHVjdHNcXHBhZ2UubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJjb250YWluZXJcIjogXCJwYWdlX2NvbnRhaW5lcl9fTU5ZOUJcIixcblx0XCJuYXZpZ2F0aW9uXCI6IFwicGFnZV9uYXZpZ2F0aW9uX18ycFJUSFwiLFxuXHRcIm5hdkNvbnRhaW5lclwiOiBcInBhZ2VfbmF2Q29udGFpbmVyX185Mk94YVwiLFxuXHRcImxvZ29cIjogXCJwYWdlX2xvZ29fX0NrWGpfXCIsXG5cdFwibmF2TWVudVwiOiBcInBhZ2VfbmF2TWVudV9fSUNicnVcIixcblx0XCJhY3RpdmVcIjogXCJwYWdlX2FjdGl2ZV9fdWVHTmVcIixcblx0XCJoZXJvXCI6IFwicGFnZV9oZXJvX19NX3czbFwiLFxuXHRcImhlcm9Db250ZW50XCI6IFwicGFnZV9oZXJvQ29udGVudF9fOGwwWDBcIixcblx0XCJoZXJvVGl0bGVcIjogXCJwYWdlX2hlcm9UaXRsZV9fdTNkQ29cIixcblx0XCJoZXJvU3VidGl0bGVcIjogXCJwYWdlX2hlcm9TdWJ0aXRsZV9fek11YXJcIixcblx0XCJwcm9kdWN0c1NlY3Rpb25cIjogXCJwYWdlX3Byb2R1Y3RzU2VjdGlvbl9fcDhCTzBcIixcblx0XCJjYXRlZ29yeUZpbHRlclwiOiBcInBhZ2VfY2F0ZWdvcnlGaWx0ZXJfX1liUktnXCIsXG5cdFwiY2F0ZWdvcnlCdG5cIjogXCJwYWdlX2NhdGVnb3J5QnRuX195NlRNd1wiLFxuXHRcInByb2R1Y3RzR3JpZFwiOiBcInBhZ2VfcHJvZHVjdHNHcmlkX19rb2l6U1wiLFxuXHRcInByb2R1Y3RDYXJkXCI6IFwicGFnZV9wcm9kdWN0Q2FyZF9fVGNUNUtcIixcblx0XCJwcm9kdWN0SW1hZ2VDb250YWluZXJcIjogXCJwYWdlX3Byb2R1Y3RJbWFnZUNvbnRhaW5lcl9fVE9PdlhcIixcblx0XCJwcm9kdWN0SW1hZ2VcIjogXCJwYWdlX3Byb2R1Y3RJbWFnZV9fUml5emxcIixcblx0XCJwcm9kdWN0T3ZlcmxheVwiOiBcInBhZ2VfcHJvZHVjdE92ZXJsYXlfXzBfOE1nXCIsXG5cdFwidmlld0J0blwiOiBcInBhZ2Vfdmlld0J0bl9fTEIzQTFcIixcblx0XCJjYXJ0QnRuXCI6IFwicGFnZV9jYXJ0QnRuX19IcDJzblwiLFxuXHRcInByb2R1Y3RJbmZvXCI6IFwicGFnZV9wcm9kdWN0SW5mb19fM2c2elNcIixcblx0XCJwcm9kdWN0TmFtZVwiOiBcInBhZ2VfcHJvZHVjdE5hbWVfX1M4T2xGXCIsXG5cdFwicHJvZHVjdERlc2NyaXB0aW9uXCI6IFwicGFnZV9wcm9kdWN0RGVzY3JpcHRpb25fXzIwNDU4XCIsXG5cdFwicHJvZHVjdEZvb3RlclwiOiBcInBhZ2VfcHJvZHVjdEZvb3Rlcl9fR0lZdzdcIixcblx0XCJwcm9kdWN0UHJpY2VcIjogXCJwYWdlX3Byb2R1Y3RQcmljZV9fR1Exb1JcIixcblx0XCJpbnF1aXJlQnRuXCI6IFwicGFnZV9pbnF1aXJlQnRuX19BRENXU1wiLFxuXHRcImN0YVNlY3Rpb25cIjogXCJwYWdlX2N0YVNlY3Rpb25fX19WcUFMXCIsXG5cdFwiY3RhQ29udGVudFwiOiBcInBhZ2VfY3RhQ29udGVudF9fX0psSF9cIixcblx0XCJjdGFCdG5cIjogXCJwYWdlX2N0YUJ0bl9fcTdfVFFcIixcblx0XCJmb290ZXJcIjogXCJwYWdlX2Zvb3Rlcl9fNF90WjRcIixcblx0XCJmb290ZXJDb250ZW50XCI6IFwicGFnZV9mb290ZXJDb250ZW50X19KX3J6cFwiLFxuXHRcImZvb3RlclNlY3Rpb25cIjogXCJwYWdlX2Zvb3RlclNlY3Rpb25fXzhoaGxUXCIsXG5cdFwiZm9vdGVyQm90dG9tXCI6IFwicGFnZV9mb290ZXJCb3R0b21fXzQyN3U2XCIsXG5cdFwibW9iaWxlTWVudVRvZ2dsZVwiOiBcInBhZ2VfbW9iaWxlTWVudVRvZ2dsZV9fQUk4SEhcIixcblx0XCJoYW1idXJnZXJMaW5lXCI6IFwicGFnZV9oYW1idXJnZXJMaW5lX19Hb3JQMlwiLFxuXHRcIm1vYmlsZU1lbnVcIjogXCJwYWdlX21vYmlsZU1lbnVfX3JqR3NBXCIsXG5cdFwibW9iaWxlTmF2TWVudVwiOiBcInBhZ2VfbW9iaWxlTmF2TWVudV9fR3NieU5cIixcblx0XCJtb2JpbGVEcm9wZG93blRvZ2dsZVwiOiBcInBhZ2VfbW9iaWxlRHJvcGRvd25Ub2dnbGVfX19yZzQ5XCIsXG5cdFwibW9iaWxlRHJvcGRvd25NZW51XCI6IFwicGFnZV9tb2JpbGVEcm9wZG93bk1lbnVfXzd3XzA3XCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjExZmYyOWQ3MTFiY1wiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/products/page.module.css\n");

/***/ }),

/***/ "(ssr)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Products)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components */ \"(ssr)/./src/components/index.ts\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./page.module.css */ \"(ssr)/./src/app/products/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Products() {\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const categories = [\n        {\n            id: 'all',\n            name: 'All Products'\n        },\n        {\n            id: 'fireplaces',\n            name: 'Fireplaces'\n        },\n        {\n            id: 'garden',\n            name: 'Garden Features'\n        },\n        {\n            id: 'architectural',\n            name: 'Architectural'\n        },\n        {\n            id: 'decorative',\n            name: 'Decorative'\n        }\n    ];\n    const products = [\n        {\n            id: \"1\",\n            name: \"Classic Fireplace Mantel\",\n            category: \"fireplaces\",\n            price: 2500,\n            image: \"/images/fireplace-collection.jpg\",\n            description: \"Handcrafted traditional mantel with intricate detailing\"\n        },\n        {\n            id: \"2\",\n            name: \"Modern Fireplace Surround\",\n            category: \"fireplaces\",\n            price: 3200,\n            image: \"/images/fireplace-collection.jpg\",\n            description: \"Contemporary design with clean lines and elegant finish\"\n        },\n        {\n            id: \"3\",\n            name: \"Garden Fountain\",\n            category: \"garden\",\n            price: 1800,\n            image: \"/images/garden-collection.jpg\",\n            description: \"Three-tier fountain perfect for outdoor spaces\"\n        },\n        {\n            id: \"4\",\n            name: \"Decorative Planters\",\n            category: \"garden\",\n            price: 450,\n            image: \"/images/garden-collection.jpg\",\n            description: \"Set of elegant planters for garden landscaping\"\n        },\n        {\n            id: \"5\",\n            name: \"Classical Columns\",\n            category: \"architectural\",\n            price: 1200,\n            image: \"/images/architectural-collection.jpg\",\n            description: \"Corinthian style columns for grand entrances\"\n        },\n        {\n            id: \"6\",\n            name: \"Decorative Balustrade\",\n            category: \"architectural\",\n            price: 800,\n            image: \"/images/architectural-collection.jpg\",\n            description: \"Ornate balustrade for staircases and terraces\"\n        },\n        {\n            id: \"7\",\n            name: \"Wall Medallions\",\n            category: \"decorative\",\n            price: 350,\n            image: \"/images/architectural-collection.jpg\",\n            description: \"Decorative wall medallions for interior accent\"\n        },\n        {\n            id: \"8\",\n            name: \"Ornamental Corbels\",\n            category: \"decorative\",\n            price: 280,\n            image: \"/images/architectural-collection.jpg\",\n            description: \"Supporting brackets with decorative styling\"\n        }\n    ];\n    const filteredProducts = selectedCategory === 'all' ? products : products.filter((product)=>product.category === selectedCategory);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.Navigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().hero),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().heroContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().heroTitle),\n                            children: \"Our Products\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().heroSubtitle),\n                            children: \"Discover our complete collection of handcrafted cast stone elements, from elegant fireplaces to stunning garden features.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productsSection),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().categoryFilter),\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: `${(_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().categoryBtn)} ${selectedCategory === category.id ? (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().active) : ''}`,\n                                onClick: ()=>setSelectedCategory(category.id),\n                                children: category.name\n                            }, category.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productsGrid),\n                        children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productCard),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productImageContainer),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: product.image,\n                                                alt: product.name,\n                                                width: 400,\n                                                height: 300,\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productImage)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productOverlay),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().viewBtn),\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.AddToCartButton, {\n                                                        product: {\n                                                            id: product.id,\n                                                            name: product.name,\n                                                            price: product.price,\n                                                            image: product.image,\n                                                            category: product.category,\n                                                            description: product.description\n                                                        },\n                                                        variant: \"primary\",\n                                                        size: \"medium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productInfo),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productName),\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productDescription),\n                                                children: product.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productFooter),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().productPrice),\n                                                        children: [\n                                                            \"$\",\n                                                            product.price.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.AddToCartButton, {\n                                                        product: {\n                                                            id: product.id,\n                                                            name: product.name,\n                                                            price: product.price,\n                                                            image: product.image,\n                                                            category: product.category,\n                                                            description: product.description\n                                                        },\n                                                        variant: \"outline\",\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, product.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().ctaSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().ctaContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Need Custom Design?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Our master craftsmen can create bespoke cast stone pieces tailored to your specific requirements.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_4___default().ctaBtn),\n                            children: \"Request Custom Quote\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/products/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/cart/AddToCartButton.module.css":
/*!********************************************************!*\
  !*** ./src/components/cart/AddToCartButton.module.css ***!
  \********************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"addToCartButton\": \"AddToCartButton_addToCartButton__UeAA8\",\n\t\"primary\": \"AddToCartButton_primary__nObA3\",\n\t\"secondary\": \"AddToCartButton_secondary__2Q9uu\",\n\t\"outline\": \"AddToCartButton_outline__g35qH\",\n\t\"small\": \"AddToCartButton_small__y17jf\",\n\t\"medium\": \"AddToCartButton_medium__AC18e\",\n\t\"large\": \"AddToCartButton_large__uuri9\",\n\t\"loading\": \"AddToCartButton_loading__dkmYR\",\n\t\"success\": \"AddToCartButton_success__ov0hC\",\n\t\"inCart\": \"AddToCartButton_inCart__BGkau\",\n\t\"disabled\": \"AddToCartButton_disabled__xUbkt\",\n\t\"text\": \"AddToCartButton_text__GHUHN\",\n\t\"spinner\": \"AddToCartButton_spinner__TDX27\",\n\t\"spin\": \"AddToCartButton_spin__dsVqO\"\n};\n\nmodule.exports.__checksum = \"81382b004fe9\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jYXJ0L0FkZFRvQ2FydEJ1dHRvbi5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGNhcnRcXEFkZFRvQ2FydEJ1dHRvbi5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImFkZFRvQ2FydEJ1dHRvblwiOiBcIkFkZFRvQ2FydEJ1dHRvbl9hZGRUb0NhcnRCdXR0b25fX1VlQUE4XCIsXG5cdFwicHJpbWFyeVwiOiBcIkFkZFRvQ2FydEJ1dHRvbl9wcmltYXJ5X19uT2JBM1wiLFxuXHRcInNlY29uZGFyeVwiOiBcIkFkZFRvQ2FydEJ1dHRvbl9zZWNvbmRhcnlfXzJROXV1XCIsXG5cdFwib3V0bGluZVwiOiBcIkFkZFRvQ2FydEJ1dHRvbl9vdXRsaW5lX19nMzVxSFwiLFxuXHRcInNtYWxsXCI6IFwiQWRkVG9DYXJ0QnV0dG9uX3NtYWxsX195MTdqZlwiLFxuXHRcIm1lZGl1bVwiOiBcIkFkZFRvQ2FydEJ1dHRvbl9tZWRpdW1fX0FDMThlXCIsXG5cdFwibGFyZ2VcIjogXCJBZGRUb0NhcnRCdXR0b25fbGFyZ2VfX3V1cmk5XCIsXG5cdFwibG9hZGluZ1wiOiBcIkFkZFRvQ2FydEJ1dHRvbl9sb2FkaW5nX19ka21ZUlwiLFxuXHRcInN1Y2Nlc3NcIjogXCJBZGRUb0NhcnRCdXR0b25fc3VjY2Vzc19fb3YwaENcIixcblx0XCJpbkNhcnRcIjogXCJBZGRUb0NhcnRCdXR0b25faW5DYXJ0X19CR2thdVwiLFxuXHRcImRpc2FibGVkXCI6IFwiQWRkVG9DYXJ0QnV0dG9uX2Rpc2FibGVkX194VWJrdFwiLFxuXHRcInRleHRcIjogXCJBZGRUb0NhcnRCdXR0b25fdGV4dF9fR0hVSE5cIixcblx0XCJzcGlubmVyXCI6IFwiQWRkVG9DYXJ0QnV0dG9uX3NwaW5uZXJfX1REWDI3XCIsXG5cdFwic3BpblwiOiBcIkFkZFRvQ2FydEJ1dHRvbl9zcGluX19kc1ZxT1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI4MTM4MmIwMDRmZTlcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/cart/AddToCartButton.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/cart/AddToCartButton.tsx":
/*!*************************************************!*\
  !*** ./src/components/cart/AddToCartButton.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddToCartButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,ShoppingCart!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,ShoppingCart!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,ShoppingCart!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _store_cartStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../store/cartStore */ \"(ssr)/./src/store/cartStore.ts\");\n/* harmony import */ var _AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AddToCartButton.module.css */ \"(ssr)/./src/components/cart/AddToCartButton.module.css\");\n/* harmony import */ var _AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AddToCartButton({ product, variant = 'primary', size = 'medium', className, disabled = false, showIcon = true, children }) {\n    const { addItem, items } = (0,_store_cartStore__WEBPACK_IMPORTED_MODULE_2__.useCartStore)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [justAdded, setJustAdded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isInCart = items.some((item)=>item.id === product.id);\n    const handleAddToCart = async ()=>{\n        if (disabled || isLoading) return;\n        setIsLoading(true);\n        try {\n            // Simulate API call delay for better UX\n            await new Promise((resolve)=>setTimeout(resolve, 300));\n            addItem(product);\n            setJustAdded(true);\n            // Reset the \"just added\" state after 2 seconds\n            setTimeout(()=>setJustAdded(false), 2000);\n        } catch (error) {\n            console.error('Error adding item to cart:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getButtonText = ()=>{\n        if (children) return children;\n        if (isLoading) return 'Adding...';\n        if (justAdded) return 'Added!';\n        if (isInCart) return 'In Cart';\n        return 'Add to Cart';\n    };\n    const getIcon = ()=>{\n        if (!showIcon) return null;\n        if (isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: (_AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\AddToCartButton.tsx\",\n            lineNumber: 64,\n            columnNumber: 27\n        }, this);\n        if (justAdded) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\AddToCartButton.tsx\",\n            lineNumber: 65,\n            columnNumber: 27\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\AddToCartButton.tsx\",\n            lineNumber: 66,\n            columnNumber: 12\n        }, this);\n    };\n    const buttonClasses = [\n        (_AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3___default().addToCartButton),\n        (_AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3___default())[variant],\n        (_AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3___default())[size],\n        isLoading && (_AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3___default().loading),\n        justAdded && (_AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3___default().success),\n        isInCart && (_AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3___default().inCart),\n        disabled && (_AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3___default().disabled),\n        className\n    ].filter(Boolean).join(' ');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: buttonClasses,\n        onClick: handleAddToCart,\n        disabled: disabled || isLoading,\n        \"aria-label\": `Add ${product.name} to cart`,\n        children: [\n            getIcon(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (_AddToCartButton_module_css__WEBPACK_IMPORTED_MODULE_3___default().text),\n                children: getButtonText()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\AddToCartButton.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\AddToCartButton.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/cart/AddToCartButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/cart/CartIcon.module.css":
/*!*************************************************!*\
  !*** ./src/components/cart/CartIcon.module.css ***!
  \*************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"cartIcon\": \"CartIcon_cartIcon__goXOm\",\n\t\"iconWrapper\": \"CartIcon_iconWrapper__y4SgP\",\n\t\"icon\": \"CartIcon_icon__c6CEd\",\n\t\"badge\": \"CartIcon_badge__us6B1\",\n\t\"bounceIn\": \"CartIcon_bounceIn__HWcAh\"\n};\n\nmodule.exports.__checksum = \"86b764a897b0\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jYXJ0L0NhcnRJY29uLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXFBhdHJpY2tzIHdlYlxcQ2FzdC1TdG9uZVxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcY2FydFxcQ2FydEljb24ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJjYXJ0SWNvblwiOiBcIkNhcnRJY29uX2NhcnRJY29uX19nb1hPbVwiLFxuXHRcImljb25XcmFwcGVyXCI6IFwiQ2FydEljb25faWNvbldyYXBwZXJfX3k0U2dQXCIsXG5cdFwiaWNvblwiOiBcIkNhcnRJY29uX2ljb25fX2M2Q0VkXCIsXG5cdFwiYmFkZ2VcIjogXCJDYXJ0SWNvbl9iYWRnZV9fdXM2QjFcIixcblx0XCJib3VuY2VJblwiOiBcIkNhcnRJY29uX2JvdW5jZUluX19IV2NBaFwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI4NmI3NjRhODk3YjBcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/cart/CartIcon.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/cart/CartIcon.tsx":
/*!******************************************!*\
  !*** ./src/components/cart/CartIcon.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CartIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingBag!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _store_cartStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../store/cartStore */ \"(ssr)/./src/store/cartStore.ts\");\n/* harmony import */ var _CartIcon_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CartIcon.module.css */ \"(ssr)/./src/components/cart/CartIcon.module.css\");\n/* harmony import */ var _CartIcon_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_CartIcon_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction CartIcon({ className, onClick }) {\n    const { items, toggleCart } = (0,_store_cartStore__WEBPACK_IMPORTED_MODULE_1__.useCartStore)();\n    const itemCount = items.reduce((count, item)=>count + item.quantity, 0);\n    const handleClick = ()=>{\n        if (onClick) {\n            onClick();\n        } else {\n            toggleCart();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: `${(_CartIcon_module_css__WEBPACK_IMPORTED_MODULE_2___default().cartIcon)} ${className || ''}`,\n        onClick: handleClick,\n        \"aria-label\": `Shopping cart with ${itemCount} items`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CartIcon_module_css__WEBPACK_IMPORTED_MODULE_2___default().iconWrapper),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: (_CartIcon_module_css__WEBPACK_IMPORTED_MODULE_2___default().icon)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartIcon.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                itemCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (_CartIcon_module_css__WEBPACK_IMPORTED_MODULE_2___default().badge),\n                    \"aria-label\": `${itemCount} items in cart`,\n                    children: itemCount > 99 ? '99+' : itemCount\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartIcon.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartIcon.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartIcon.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/cart/CartIcon.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/cart/CartSidebar.module.css":
/*!****************************************************!*\
  !*** ./src/components/cart/CartSidebar.module.css ***!
  \****************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"overlay\": \"CartSidebar_overlay__mqzKG\",\n\t\"fadeIn\": \"CartSidebar_fadeIn__nqWOk\",\n\t\"sidebar\": \"CartSidebar_sidebar__2_A5n\",\n\t\"slideIn\": \"CartSidebar_slideIn__pC9qA\",\n\t\"header\": \"CartSidebar_header__o1Au0\",\n\t\"headerContent\": \"CartSidebar_headerContent__EmC2j\",\n\t\"headerIcon\": \"CartSidebar_headerIcon__bLyLa\",\n\t\"title\": \"CartSidebar_title__T_BOr\",\n\t\"itemCount\": \"CartSidebar_itemCount__KfR58\",\n\t\"closeButton\": \"CartSidebar_closeButton__8Ydlb\",\n\t\"content\": \"CartSidebar_content__Ru9kP\",\n\t\"emptyCart\": \"CartSidebar_emptyCart__3D1F3\",\n\t\"emptyIcon\": \"CartSidebar_emptyIcon__IhnS7\",\n\t\"shopButton\": \"CartSidebar_shopButton__jQbqs\",\n\t\"items\": \"CartSidebar_items__LfUBW\",\n\t\"item\": \"CartSidebar_item__JsbsR\",\n\t\"itemImage\": \"CartSidebar_itemImage__Qm_S7\",\n\t\"image\": \"CartSidebar_image__MCno_\",\n\t\"imagePlaceholder\": \"CartSidebar_imagePlaceholder__v63ez\",\n\t\"itemDetails\": \"CartSidebar_itemDetails__q83BZ\",\n\t\"itemName\": \"CartSidebar_itemName__kbsGh\",\n\t\"itemCategory\": \"CartSidebar_itemCategory__Vn8aY\",\n\t\"itemPrice\": \"CartSidebar_itemPrice__jWgLO\",\n\t\"quantityControls\": \"CartSidebar_quantityControls__cwH5X\",\n\t\"quantityButton\": \"CartSidebar_quantityButton__rAkZS\",\n\t\"quantity\": \"CartSidebar_quantity__yNcp1\",\n\t\"itemActions\": \"CartSidebar_itemActions__P0Tbw\",\n\t\"itemTotal\": \"CartSidebar_itemTotal__EXHEC\",\n\t\"removeButton\": \"CartSidebar_removeButton__v8Ns5\",\n\t\"summary\": \"CartSidebar_summary__oV_5c\",\n\t\"summaryRow\": \"CartSidebar_summaryRow__PZW5e\",\n\t\"summaryTotal\": \"CartSidebar_summaryTotal__vtFkT\",\n\t\"freeShippingNote\": \"CartSidebar_freeShippingNote__knVul\",\n\t\"actions\": \"CartSidebar_actions__PP6QO\",\n\t\"checkoutButton\": \"CartSidebar_checkoutButton__HuDB4\",\n\t\"viewCartButton\": \"CartSidebar_viewCartButton__K05HQ\"\n};\n\nmodule.exports.__checksum = \"ffd636dd035e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jYXJ0L0NhcnRTaWRlYmFyLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVbWVyIEZhcm9vcVxcRGVza3RvcFxcUGF0cmlja3Mgd2ViXFxDYXN0LVN0b25lXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxjYXJ0XFxDYXJ0U2lkZWJhci5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIm92ZXJsYXlcIjogXCJDYXJ0U2lkZWJhcl9vdmVybGF5X19tcXpLR1wiLFxuXHRcImZhZGVJblwiOiBcIkNhcnRTaWRlYmFyX2ZhZGVJbl9fbnFXT2tcIixcblx0XCJzaWRlYmFyXCI6IFwiQ2FydFNpZGViYXJfc2lkZWJhcl9fMl9BNW5cIixcblx0XCJzbGlkZUluXCI6IFwiQ2FydFNpZGViYXJfc2xpZGVJbl9fcEM5cUFcIixcblx0XCJoZWFkZXJcIjogXCJDYXJ0U2lkZWJhcl9oZWFkZXJfX28xQXUwXCIsXG5cdFwiaGVhZGVyQ29udGVudFwiOiBcIkNhcnRTaWRlYmFyX2hlYWRlckNvbnRlbnRfX0VtQzJqXCIsXG5cdFwiaGVhZGVySWNvblwiOiBcIkNhcnRTaWRlYmFyX2hlYWRlckljb25fX2JMeUxhXCIsXG5cdFwidGl0bGVcIjogXCJDYXJ0U2lkZWJhcl90aXRsZV9fVF9CT3JcIixcblx0XCJpdGVtQ291bnRcIjogXCJDYXJ0U2lkZWJhcl9pdGVtQ291bnRfX0tmUjU4XCIsXG5cdFwiY2xvc2VCdXR0b25cIjogXCJDYXJ0U2lkZWJhcl9jbG9zZUJ1dHRvbl9fOFlkbGJcIixcblx0XCJjb250ZW50XCI6IFwiQ2FydFNpZGViYXJfY29udGVudF9fUnU5a1BcIixcblx0XCJlbXB0eUNhcnRcIjogXCJDYXJ0U2lkZWJhcl9lbXB0eUNhcnRfXzNEMUYzXCIsXG5cdFwiZW1wdHlJY29uXCI6IFwiQ2FydFNpZGViYXJfZW1wdHlJY29uX19JaG5TN1wiLFxuXHRcInNob3BCdXR0b25cIjogXCJDYXJ0U2lkZWJhcl9zaG9wQnV0dG9uX19qUWJxc1wiLFxuXHRcIml0ZW1zXCI6IFwiQ2FydFNpZGViYXJfaXRlbXNfX0xmVUJXXCIsXG5cdFwiaXRlbVwiOiBcIkNhcnRTaWRlYmFyX2l0ZW1fX0pzYnNSXCIsXG5cdFwiaXRlbUltYWdlXCI6IFwiQ2FydFNpZGViYXJfaXRlbUltYWdlX19RbV9TN1wiLFxuXHRcImltYWdlXCI6IFwiQ2FydFNpZGViYXJfaW1hZ2VfX01Dbm9fXCIsXG5cdFwiaW1hZ2VQbGFjZWhvbGRlclwiOiBcIkNhcnRTaWRlYmFyX2ltYWdlUGxhY2Vob2xkZXJfX3Y2M2V6XCIsXG5cdFwiaXRlbURldGFpbHNcIjogXCJDYXJ0U2lkZWJhcl9pdGVtRGV0YWlsc19fcTgzQlpcIixcblx0XCJpdGVtTmFtZVwiOiBcIkNhcnRTaWRlYmFyX2l0ZW1OYW1lX19rYnNHaFwiLFxuXHRcIml0ZW1DYXRlZ29yeVwiOiBcIkNhcnRTaWRlYmFyX2l0ZW1DYXRlZ29yeV9fVm44YVlcIixcblx0XCJpdGVtUHJpY2VcIjogXCJDYXJ0U2lkZWJhcl9pdGVtUHJpY2VfX2pXZ0xPXCIsXG5cdFwicXVhbnRpdHlDb250cm9sc1wiOiBcIkNhcnRTaWRlYmFyX3F1YW50aXR5Q29udHJvbHNfX2N3SDVYXCIsXG5cdFwicXVhbnRpdHlCdXR0b25cIjogXCJDYXJ0U2lkZWJhcl9xdWFudGl0eUJ1dHRvbl9fckFrWlNcIixcblx0XCJxdWFudGl0eVwiOiBcIkNhcnRTaWRlYmFyX3F1YW50aXR5X195TmNwMVwiLFxuXHRcIml0ZW1BY3Rpb25zXCI6IFwiQ2FydFNpZGViYXJfaXRlbUFjdGlvbnNfX1AwVGJ3XCIsXG5cdFwiaXRlbVRvdGFsXCI6IFwiQ2FydFNpZGViYXJfaXRlbVRvdGFsX19FWEhFQ1wiLFxuXHRcInJlbW92ZUJ1dHRvblwiOiBcIkNhcnRTaWRlYmFyX3JlbW92ZUJ1dHRvbl9fdjhOczVcIixcblx0XCJzdW1tYXJ5XCI6IFwiQ2FydFNpZGViYXJfc3VtbWFyeV9fb1ZfNWNcIixcblx0XCJzdW1tYXJ5Um93XCI6IFwiQ2FydFNpZGViYXJfc3VtbWFyeVJvd19fUFpXNWVcIixcblx0XCJzdW1tYXJ5VG90YWxcIjogXCJDYXJ0U2lkZWJhcl9zdW1tYXJ5VG90YWxfX3Z0RmtUXCIsXG5cdFwiZnJlZVNoaXBwaW5nTm90ZVwiOiBcIkNhcnRTaWRlYmFyX2ZyZWVTaGlwcGluZ05vdGVfX2tuVnVsXCIsXG5cdFwiYWN0aW9uc1wiOiBcIkNhcnRTaWRlYmFyX2FjdGlvbnNfX1BQNlFPXCIsXG5cdFwiY2hlY2tvdXRCdXR0b25cIjogXCJDYXJ0U2lkZWJhcl9jaGVja291dEJ1dHRvbl9fSHVEQjRcIixcblx0XCJ2aWV3Q2FydEJ1dHRvblwiOiBcIkNhcnRTaWRlYmFyX3ZpZXdDYXJ0QnV0dG9uX19LMDVIUVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJmZmQ2MzZkZDAzNWVcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/cart/CartSidebar.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/cart/CartSidebar.tsx":
/*!*********************************************!*\
  !*** ./src/components/cart/CartSidebar.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CartSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus,ShoppingBag,Trash2,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus,ShoppingBag,Trash2,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus,ShoppingBag,Trash2,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus,ShoppingBag,Trash2,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus,ShoppingBag,Trash2,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _store_cartStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../store/cartStore */ \"(ssr)/./src/store/cartStore.ts\");\n/* harmony import */ var _CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CartSidebar.module.css */ \"(ssr)/./src/components/cart/CartSidebar.module.css\");\n/* harmony import */ var _CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction CartSidebar() {\n    const { items, isOpen, subtotal, tax, shipping, total, toggleCart, updateQuantity, removeItem, calculateTotals } = (0,_store_cartStore__WEBPACK_IMPORTED_MODULE_2__.useCartStore)();\n    // Calculate totals when component mounts or items change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartSidebar.useEffect\": ()=>{\n            calculateTotals();\n        }\n    }[\"CartSidebar.useEffect\"], [\n        items,\n        calculateTotals\n    ]);\n    // Prevent body scroll when cart is open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartSidebar.useEffect\": ()=>{\n            if (isOpen) {\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = 'unset';\n            }\n            return ({\n                \"CartSidebar.useEffect\": ()=>{\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"CartSidebar.useEffect\"];\n        }\n    }[\"CartSidebar.useEffect\"], [\n        isOpen\n    ]);\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().overlay),\n                onClick: toggleCart\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().sidebar),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerContent),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerIcon)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().title),\n                                        children: \"Shopping Cart\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().itemCount),\n                                        children: [\n                                            \"(\",\n                                            items.length,\n                                            \" items)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeButton),\n                                onClick: toggleCart,\n                                \"aria-label\": \"Close cart\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().content),\n                        children: items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyCart),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyIcon)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"Your cart is empty\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Add some beautiful cast stone pieces to get started!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/products\",\n                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().shopButton),\n                                    onClick: toggleCart,\n                                    children: \"Shop Now\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().items),\n                                    children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().item),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().itemImage),\n                                                    children: item.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        src: item.image,\n                                                        alt: item.name,\n                                                        width: 80,\n                                                        height: 80,\n                                                        className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().image)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().imagePlaceholder),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().itemDetails),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().itemName),\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        item.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().itemCategory),\n                                                            children: item.category\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().itemPrice),\n                                                            children: (0,_store_cartStore__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(item.price)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().quantityControls),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().quantityButton),\n                                                                    onClick: ()=>updateQuantity(item.id, item.quantity - 1),\n                                                                    \"aria-label\": \"Decrease quantity\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                                        lineNumber: 113,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                                    lineNumber: 108,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().quantity),\n                                                                    children: item.quantity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                                    lineNumber: 115,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().quantityButton),\n                                                                    onClick: ()=>updateQuantity(item.id, item.quantity + 1),\n                                                                    \"aria-label\": \"Increase quantity\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                                        lineNumber: 121,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                                    lineNumber: 116,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().itemActions),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().itemTotal),\n                                                            children: (0,_store_cartStore__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(item.price * item.quantity)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().removeButton),\n                                                            onClick: ()=>removeItem(item.id),\n                                                            \"aria-label\": `Remove ${item.name} from cart`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, item.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().summary),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().summaryRow),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Subtotal:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (0,_store_cartStore__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(subtotal)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().summaryRow),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Tax:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (0,_store_cartStore__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(tax)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().summaryRow),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Shipping:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: shipping === 0 ? 'Free' : (0,_store_cartStore__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(shipping)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().summaryTotal),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Total:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (0,_store_cartStore__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this),\n                                        subtotal > 0 && subtotal < 500 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().freeShippingNote),\n                                            children: [\n                                                \"Add \",\n                                                (0,_store_cartStore__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(500 - subtotal),\n                                                \" more for free shipping!\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().actions),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/checkout\",\n                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().checkoutButton),\n                                            onClick: toggleCart,\n                                            children: \"Proceed to Checkout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/cart\",\n                                            className: (_CartSidebar_module_css__WEBPACK_IMPORTED_MODULE_3___default().viewCartButton),\n                                            onClick: toggleCart,\n                                            children: \"View Full Cart\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\cart\\\\CartSidebar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/cart/CartSidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*******************************************************!*\
  !*** ./src/components/home/<USER>
  \*******************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"catalogSection\": \"CatalogSection_catalogSection__xWHSY\",\n\t\"catalogContainer\": \"CatalogSection_catalogContainer__pOYTt\",\n\t\"catalogContent\": \"CatalogSection_catalogContent__Ut8dn\",\n\t\"catalogText\": \"CatalogSection_catalogText__Q2fXT\",\n\t\"catalogBtn\": \"CatalogSection_catalogBtn__M84YU\",\n\t\"catalogImage\": \"CatalogSection_catalogImage__mwGOP\",\n\t\"catalogImg\": \"CatalogSection_catalogImg__Es0x_\"\n};\n\nmodule.exports.__checksum = \"d71f2d817a90\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL0NhdGFsb2dTZWN0aW9uLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGhvbWVcXENhdGFsb2dTZWN0aW9uLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY2F0YWxvZ1NlY3Rpb25cIjogXCJDYXRhbG9nU2VjdGlvbl9jYXRhbG9nU2VjdGlvbl9feFdIU1lcIixcblx0XCJjYXRhbG9nQ29udGFpbmVyXCI6IFwiQ2F0YWxvZ1NlY3Rpb25fY2F0YWxvZ0NvbnRhaW5lcl9fcE9ZVHRcIixcblx0XCJjYXRhbG9nQ29udGVudFwiOiBcIkNhdGFsb2dTZWN0aW9uX2NhdGFsb2dDb250ZW50X19VdDhkblwiLFxuXHRcImNhdGFsb2dUZXh0XCI6IFwiQ2F0YWxvZ1NlY3Rpb25fY2F0YWxvZ1RleHRfX1EyZlhUXCIsXG5cdFwiY2F0YWxvZ0J0blwiOiBcIkNhdGFsb2dTZWN0aW9uX2NhdGFsb2dCdG5fX004NFlVXCIsXG5cdFwiY2F0YWxvZ0ltYWdlXCI6IFwiQ2F0YWxvZ1NlY3Rpb25fY2F0YWxvZ0ltYWdlX19td0dPUFwiLFxuXHRcImNhdGFsb2dJbWdcIjogXCJDYXRhbG9nU2VjdGlvbl9jYXRhbG9nSW1nX19FczB4X1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJkNzFmMmQ4MTdhOTBcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!************************************************!*\
  !*** ./src/components/home/<USER>
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CatalogSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CatalogSection.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CatalogSection({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `${(_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogSection)} ${className || ''}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogContent),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogText),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"Explore Our Complete Catalog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Browse through our comprehensive collection of cast stone products. From architectural elements to decorative pieces, find everything you need to transform your space with timeless elegance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogBtn),\n                                children: \"View Catalog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogImage),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"/images/catalog-preview.jpg\",\n                        alt: \"Cast Stone Catalog\",\n                        width: 600,\n                        height: 400,\n                        className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogImg)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!********************************************************!*\
  !*** ./src/components/home/<USER>
  \********************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"collectionsSection\": \"CollectionsGrid_collectionsSection__INn5d\",\n\t\"collectionsGrid\": \"CollectionsGrid_collectionsGrid__B6kkM\",\n\t\"collectionCard\": \"CollectionsGrid_collectionCard__qG8Ub\",\n\t\"collectionImage\": \"CollectionsGrid_collectionImage__Jw6QX\",\n\t\"collectionImg\": \"CollectionsGrid_collectionImg__QDbrX\",\n\t\"collectionOverlay\": \"CollectionsGrid_collectionOverlay__jJQyH\",\n\t\"collectionBrand\": \"CollectionsGrid_collectionBrand__16gRy\",\n\t\"collectionSubtitle\": \"CollectionsGrid_collectionSubtitle__6kv2c\",\n\t\"collectionDescription\": \"CollectionsGrid_collectionDescription__yFGMj\",\n\t\"collectionPrice\": \"CollectionsGrid_collectionPrice__OeY5J\",\n\t\"collectionButtons\": \"CollectionsGrid_collectionButtons__Xe7JP\",\n\t\"buildBtn\": \"CollectionsGrid_buildBtn__6ctOy\",\n\t\"allModelsBtn\": \"CollectionsGrid_allModelsBtn__qX2X4\"\n};\n\nmodule.exports.__checksum = \"3125fe8a11bc\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL0NvbGxlY3Rpb25zR3JpZC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVbWVyIEZhcm9vcVxcRGVza3RvcFxcUGF0cmlja3Mgd2ViXFxDYXN0LVN0b25lXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxob21lXFxDb2xsZWN0aW9uc0dyaWQubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJjb2xsZWN0aW9uc1NlY3Rpb25cIjogXCJDb2xsZWN0aW9uc0dyaWRfY29sbGVjdGlvbnNTZWN0aW9uX19JTm41ZFwiLFxuXHRcImNvbGxlY3Rpb25zR3JpZFwiOiBcIkNvbGxlY3Rpb25zR3JpZF9jb2xsZWN0aW9uc0dyaWRfX0I2a2tNXCIsXG5cdFwiY29sbGVjdGlvbkNhcmRcIjogXCJDb2xsZWN0aW9uc0dyaWRfY29sbGVjdGlvbkNhcmRfX3FHOFViXCIsXG5cdFwiY29sbGVjdGlvbkltYWdlXCI6IFwiQ29sbGVjdGlvbnNHcmlkX2NvbGxlY3Rpb25JbWFnZV9fSnc2UVhcIixcblx0XCJjb2xsZWN0aW9uSW1nXCI6IFwiQ29sbGVjdGlvbnNHcmlkX2NvbGxlY3Rpb25JbWdfX1FEYnJYXCIsXG5cdFwiY29sbGVjdGlvbk92ZXJsYXlcIjogXCJDb2xsZWN0aW9uc0dyaWRfY29sbGVjdGlvbk92ZXJsYXlfX2pKUXlIXCIsXG5cdFwiY29sbGVjdGlvbkJyYW5kXCI6IFwiQ29sbGVjdGlvbnNHcmlkX2NvbGxlY3Rpb25CcmFuZF9fMTZnUnlcIixcblx0XCJjb2xsZWN0aW9uU3VidGl0bGVcIjogXCJDb2xsZWN0aW9uc0dyaWRfY29sbGVjdGlvblN1YnRpdGxlX182a3YyY1wiLFxuXHRcImNvbGxlY3Rpb25EZXNjcmlwdGlvblwiOiBcIkNvbGxlY3Rpb25zR3JpZF9jb2xsZWN0aW9uRGVzY3JpcHRpb25fX3lGR01qXCIsXG5cdFwiY29sbGVjdGlvblByaWNlXCI6IFwiQ29sbGVjdGlvbnNHcmlkX2NvbGxlY3Rpb25QcmljZV9fT2VZNUpcIixcblx0XCJjb2xsZWN0aW9uQnV0dG9uc1wiOiBcIkNvbGxlY3Rpb25zR3JpZF9jb2xsZWN0aW9uQnV0dG9uc19fWGU3SlBcIixcblx0XCJidWlsZEJ0blwiOiBcIkNvbGxlY3Rpb25zR3JpZF9idWlsZEJ0bl9fNmN0T3lcIixcblx0XCJhbGxNb2RlbHNCdG5cIjogXCJDb2xsZWN0aW9uc0dyaWRfYWxsTW9kZWxzQnRuX19xWDJYNFwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIzMTI1ZmU4YTExYmNcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollectionsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CollectionsGrid.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CollectionsGrid({ categories, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `${(_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionsSection)} ${className || ''}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionsGrid),\n            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionCard),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionImage),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: category.image,\n                                alt: category.title,\n                                fill: true,\n                                className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionImg)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionOverlay),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionBrand),\n                                    children: category.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionSubtitle),\n                                    children: category.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionDescription),\n                                    children: category.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionPrice),\n                                    children: category.price\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionButtons),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().buildBtn),\n                                            children: \"BUILD YOURS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().allModelsBtn),\n                                            children: \"ALL MODELS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, category.id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!************************************************************!*\
  !*** ./src/components/home/<USER>
  \************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"featuredCollections\": \"FeaturedCollections_featuredCollections__RJNQj\",\n\t\"featuredGrid\": \"FeaturedCollections_featuredGrid__EVygU\",\n\t\"featuredCard\": \"FeaturedCollections_featuredCard__r7DIE\",\n\t\"featuredImage\": \"FeaturedCollections_featuredImage__9yjUq\",\n\t\"featuredImg\": \"FeaturedCollections_featuredImg__xJy_R\",\n\t\"featuredContent\": \"FeaturedCollections_featuredContent__tM1Tm\",\n\t\"featuredTitle\": \"FeaturedCollections_featuredTitle__7PpZ0\",\n\t\"featuredSubtitle\": \"FeaturedCollections_featuredSubtitle__qT5hZ\",\n\t\"featuredBtn\": \"FeaturedCollections_featuredBtn___wxeN\"\n};\n\nmodule.exports.__checksum = \"fad24dd1f4a2\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL0ZlYXR1cmVkQ29sbGVjdGlvbnMubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGhvbWVcXEZlYXR1cmVkQ29sbGVjdGlvbnMubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJmZWF0dXJlZENvbGxlY3Rpb25zXCI6IFwiRmVhdHVyZWRDb2xsZWN0aW9uc19mZWF0dXJlZENvbGxlY3Rpb25zX19SSk5RalwiLFxuXHRcImZlYXR1cmVkR3JpZFwiOiBcIkZlYXR1cmVkQ29sbGVjdGlvbnNfZmVhdHVyZWRHcmlkX19FVnlnVVwiLFxuXHRcImZlYXR1cmVkQ2FyZFwiOiBcIkZlYXR1cmVkQ29sbGVjdGlvbnNfZmVhdHVyZWRDYXJkX19yN0RJRVwiLFxuXHRcImZlYXR1cmVkSW1hZ2VcIjogXCJGZWF0dXJlZENvbGxlY3Rpb25zX2ZlYXR1cmVkSW1hZ2VfXzl5alVxXCIsXG5cdFwiZmVhdHVyZWRJbWdcIjogXCJGZWF0dXJlZENvbGxlY3Rpb25zX2ZlYXR1cmVkSW1nX194SnlfUlwiLFxuXHRcImZlYXR1cmVkQ29udGVudFwiOiBcIkZlYXR1cmVkQ29sbGVjdGlvbnNfZmVhdHVyZWRDb250ZW50X190TTFUbVwiLFxuXHRcImZlYXR1cmVkVGl0bGVcIjogXCJGZWF0dXJlZENvbGxlY3Rpb25zX2ZlYXR1cmVkVGl0bGVfXzdQcFowXCIsXG5cdFwiZmVhdHVyZWRTdWJ0aXRsZVwiOiBcIkZlYXR1cmVkQ29sbGVjdGlvbnNfZmVhdHVyZWRTdWJ0aXRsZV9fcVQ1aFpcIixcblx0XCJmZWF0dXJlZEJ0blwiOiBcIkZlYXR1cmVkQ29sbGVjdGlvbnNfZmVhdHVyZWRCdG5fX193eGVOXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImZhZDI0ZGQxZjRhMlwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*****************************************************!*\
  !*** ./src/components/home/<USER>
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturedCollections)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FeaturedCollections.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction FeaturedCollections({ featuredProducts, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `${(_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredCollections)} ${className || ''}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredGrid),\n            children: featuredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredCard),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredImage),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: product.image,\n                                alt: product.title,\n                                fill: true,\n                                className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredImg)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredTitle),\n                                    children: product.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredSubtitle),\n                                    children: product.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredBtn),\n                                    children: product.buttonText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, product.id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!****************************************************!*\
  !*** ./src/components/home/<USER>
  \****************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"hero\": \"HeroSection_hero__tVlJy\",\n\t\"videoBackground\": \"HeroSection_videoBackground__1b56U\",\n\t\"heroVideo\": \"HeroSection_heroVideo__wmHr5\",\n\t\"videoOverlay\": \"HeroSection_videoOverlay__GbFoP\",\n\t\"heroContent\": \"HeroSection_heroContent__VYZbs\",\n\t\"heroTitle\": \"HeroSection_heroTitle__nnst_\",\n\t\"highlight\": \"HeroSection_highlight__Wgy18\",\n\t\"heroSubtitle\": \"HeroSection_heroSubtitle__J_PUa\",\n\t\"heroActions\": \"HeroSection_heroActions__KheHS\",\n\t\"primaryBtn\": \"HeroSection_primaryBtn__ajxDg\",\n\t\"secondaryBtn\": \"HeroSection_secondaryBtn__dFomW\"\n};\n\nmodule.exports.__checksum = \"4ecd093e3a8d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL0hlcm9TZWN0aW9uLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXFBhdHJpY2tzIHdlYlxcQ2FzdC1TdG9uZVxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcaG9tZVxcSGVyb1NlY3Rpb24ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJoZXJvXCI6IFwiSGVyb1NlY3Rpb25faGVyb19fdFZsSnlcIixcblx0XCJ2aWRlb0JhY2tncm91bmRcIjogXCJIZXJvU2VjdGlvbl92aWRlb0JhY2tncm91bmRfXzFiNTZVXCIsXG5cdFwiaGVyb1ZpZGVvXCI6IFwiSGVyb1NlY3Rpb25faGVyb1ZpZGVvX193bUhyNVwiLFxuXHRcInZpZGVvT3ZlcmxheVwiOiBcIkhlcm9TZWN0aW9uX3ZpZGVvT3ZlcmxheV9fR2JGb1BcIixcblx0XCJoZXJvQ29udGVudFwiOiBcIkhlcm9TZWN0aW9uX2hlcm9Db250ZW50X19WWVpic1wiLFxuXHRcImhlcm9UaXRsZVwiOiBcIkhlcm9TZWN0aW9uX2hlcm9UaXRsZV9fbm5zdF9cIixcblx0XCJoaWdobGlnaHRcIjogXCJIZXJvU2VjdGlvbl9oaWdobGlnaHRfX1dneTE4XCIsXG5cdFwiaGVyb1N1YnRpdGxlXCI6IFwiSGVyb1NlY3Rpb25faGVyb1N1YnRpdGxlX19KX1BVYVwiLFxuXHRcImhlcm9BY3Rpb25zXCI6IFwiSGVyb1NlY3Rpb25faGVyb0FjdGlvbnNfX0toZUhTXCIsXG5cdFwicHJpbWFyeUJ0blwiOiBcIkhlcm9TZWN0aW9uX3ByaW1hcnlCdG5fX2FqeERnXCIsXG5cdFwic2Vjb25kYXJ5QnRuXCI6IFwiSGVyb1NlY3Rpb25fc2Vjb25kYXJ5QnRuX19kRm9tV1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI0ZWNkMDkzZTNhOGRcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*********************************************!*\
  !*** ./src/components/home/<USER>
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./HeroSection.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction HeroSection({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `${(_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().hero)} ${className || ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().videoBackground),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        autoPlay: true,\n                        muted: true,\n                        loop: true,\n                        playsInline: true,\n                        className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().heroVideo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: \"/herosection.mp4\",\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().videoOverlay)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().heroContent),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().heroTitle),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().highlight),\n                            children: \"Timeless Elegance in Cast Stone\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().heroSubtitle),\n                        children: \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().heroActions),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().primaryBtn),\n                                children: \"Explore Collection\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().secondaryBtn),\n                                children: \"Watch Our Story\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"HomePage_container__GVF35\"\n};\n\nmodule.exports.__checksum = \"1134a7422f67\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL0hvbWVQYWdlLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGhvbWVcXEhvbWVQYWdlLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY29udGFpbmVyXCI6IFwiSG9tZVBhZ2VfY29udGFpbmVyX19HVkYzNVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIxMTM0YTc0MjJmNjdcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!******************************************!*\
  !*** ./src/components/home/<USER>
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../index */ \"(ssr)/./src/components/index.ts\");\n/* harmony import */ var _HomePage_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HomePage.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _HomePage_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_HomePage_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction HomePage() {\n    const categories = [\n        {\n            id: 1,\n            title: \"911\",\n            subtitle: \"Architectural\",\n            description: \"The iconic, rear-engine sports car with exceptional performance.\",\n            image: \"/images/architectural-collection.jpg\",\n            price: \"From $1,200\",\n            link: \"/products/architectural\"\n        },\n        {\n            id: 2,\n            title: \"718\",\n            subtitle: \"Designer\",\n            description: \"The mid-engine sports car for two made for pure driving pleasure.\",\n            image: \"/images/fireplace-collection.jpg\",\n            price: \"From $2,500\",\n            link: \"/products/designer\"\n        },\n        {\n            id: 3,\n            title: \"Taycan\",\n            subtitle: \"Limited Edition\",\n            description: \"The soul, electrified. Pure Porsche performance with zero emissions.\",\n            image: \"/images/garden-collection.jpg\",\n            price: \"From $3,800\",\n            link: \"/products/limited-edition\"\n        },\n        {\n            id: 4,\n            title: \"Panamera\",\n            subtitle: \"Sealer Program\",\n            description: \"The luxury sports sedan that combines comfort with performance.\",\n            image: \"/images/hero-cast-stone.jpg\",\n            price: \"From $150\",\n            link: \"/products/sealers\"\n        }\n    ];\n    const featuredProducts = [\n        {\n            id: 1,\n            title: \"DESIGNER'S PICKS\",\n            subtitle: \"A peek inside our designer's shopping cart.\",\n            image: \"/images/fireplace-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/designer-picks\"\n        },\n        {\n            id: 2,\n            title: \"THE CAST STONE SHOP\",\n            subtitle: \"The best of the best, from fireplaces and fountains to architectural elements.\",\n            image: \"/images/garden-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/cast-stone\"\n        },\n        {\n            id: 3,\n            title: \"ARCHITECTURAL ELEMENTS\",\n            subtitle: \"Clean, luxurious, results-driven architectural cast stone pieces.\",\n            image: \"/images/architectural-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/architectural\"\n        },\n        {\n            id: 4,\n            title: \"PREMIUM COLLECTION\",\n            subtitle: \"Classics, reimagined for the modern home.\",\n            image: \"/images/hero-cast-stone.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/premium\"\n        }\n    ];\n    const testimonials = [\n        {\n            id: 1,\n            name: \"Sarah Johnson\",\n            company: \"Johnson Architecture\",\n            text: \"Cast Stone's architectural elements transformed our project. The quality and craftsmanship are unmatched.\",\n            rating: 5\n        },\n        {\n            id: 2,\n            name: \"Michael Chen\",\n            company: \"Elite Homes\",\n            text: \"We've been using Cast Stone products for over 10 years. Their consistency and beauty never disappoint.\",\n            rating: 5\n        },\n        {\n            id: 3,\n            name: \"Emma Rodriguez\",\n            company: \"Rodriguez Design Studio\",\n            text: \"The limited edition pieces add such elegance to our high-end residential projects.\",\n            rating: 5\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_HomePage_module_css__WEBPACK_IMPORTED_MODULE_2___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.Navigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.HeroSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.CollectionsGrid, {\n                categories: categories\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.CatalogSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.FeaturedCollections, {\n                featuredProducts: featuredProducts\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.TestimonialsSection, {\n                testimonials: testimonials\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!************************************************************!*\
  !*** ./src/components/home/<USER>
  \************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"testimonialsSection\": \"TestimonialsSection_testimonialsSection__eFqMD\",\n\t\"sectionHeader\": \"TestimonialsSection_sectionHeader__P_QS4\",\n\t\"testimonialsCarousel\": \"TestimonialsSection_testimonialsCarousel__IrvAW\",\n\t\"testimonialSlide\": \"TestimonialsSection_testimonialSlide__jWab0\",\n\t\"active\": \"TestimonialsSection_active__2TCrK\",\n\t\"testimonialCard\": \"TestimonialsSection_testimonialCard__g3Le5\",\n\t\"stars\": \"TestimonialsSection_stars__sPqUh\",\n\t\"star\": \"TestimonialsSection_star__r9WUB\",\n\t\"testimonialText\": \"TestimonialsSection_testimonialText__rv9eB\",\n\t\"testimonialAuthor\": \"TestimonialsSection_testimonialAuthor__tNCS7\",\n\t\"testimonialControls\": \"TestimonialsSection_testimonialControls__WZrGJ\",\n\t\"testimonialDot\": \"TestimonialsSection_testimonialDot__WabFU\",\n\t\"activeDot\": \"TestimonialsSection_activeDot__NBmoY\"\n};\n\nmodule.exports.__checksum = \"c53e3ec32d5c\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL1Rlc3RpbW9uaWFsc1NlY3Rpb24ubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXFBhdHJpY2tzIHdlYlxcQ2FzdC1TdG9uZVxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcaG9tZVxcVGVzdGltb25pYWxzU2VjdGlvbi5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInRlc3RpbW9uaWFsc1NlY3Rpb25cIjogXCJUZXN0aW1vbmlhbHNTZWN0aW9uX3Rlc3RpbW9uaWFsc1NlY3Rpb25fX2VGcU1EXCIsXG5cdFwic2VjdGlvbkhlYWRlclwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fc2VjdGlvbkhlYWRlcl9fUF9RUzRcIixcblx0XCJ0ZXN0aW1vbmlhbHNDYXJvdXNlbFwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fdGVzdGltb25pYWxzQ2Fyb3VzZWxfX0lydkFXXCIsXG5cdFwidGVzdGltb25pYWxTbGlkZVwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fdGVzdGltb25pYWxTbGlkZV9faldhYjBcIixcblx0XCJhY3RpdmVcIjogXCJUZXN0aW1vbmlhbHNTZWN0aW9uX2FjdGl2ZV9fMlRDcktcIixcblx0XCJ0ZXN0aW1vbmlhbENhcmRcIjogXCJUZXN0aW1vbmlhbHNTZWN0aW9uX3Rlc3RpbW9uaWFsQ2FyZF9fZzNMZTVcIixcblx0XCJzdGFyc1wiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fc3RhcnNfX3NQcVVoXCIsXG5cdFwic3RhclwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fc3Rhcl9fcjlXVUJcIixcblx0XCJ0ZXN0aW1vbmlhbFRleHRcIjogXCJUZXN0aW1vbmlhbHNTZWN0aW9uX3Rlc3RpbW9uaWFsVGV4dF9fcnY5ZUJcIixcblx0XCJ0ZXN0aW1vbmlhbEF1dGhvclwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fdGVzdGltb25pYWxBdXRob3JfX3ROQ1M3XCIsXG5cdFwidGVzdGltb25pYWxDb250cm9sc1wiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fdGVzdGltb25pYWxDb250cm9sc19fV1pyR0pcIixcblx0XCJ0ZXN0aW1vbmlhbERvdFwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fdGVzdGltb25pYWxEb3RfX1dhYkZVXCIsXG5cdFwiYWN0aXZlRG90XCI6IFwiVGVzdGltb25pYWxzU2VjdGlvbl9hY3RpdmVEb3RfX05CbW9ZXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImM1M2UzZWMzMmQ1Y1wiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*****************************************************!*\
  !*** ./src/components/home/<USER>
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TestimonialsSection.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TestimonialsSection({ testimonials, className }) {\n    const [currentTestimonial, setCurrentTestimonial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestimonialsSection.useEffect\": ()=>{\n            const testimonialInterval = setInterval({\n                \"TestimonialsSection.useEffect.testimonialInterval\": ()=>{\n                    setCurrentTestimonial({\n                        \"TestimonialsSection.useEffect.testimonialInterval\": (prev)=>(prev + 1) % testimonials.length\n                    }[\"TestimonialsSection.useEffect.testimonialInterval\"]);\n                }\n            }[\"TestimonialsSection.useEffect.testimonialInterval\"], 6000);\n            return ({\n                \"TestimonialsSection.useEffect\": ()=>clearInterval(testimonialInterval)\n            })[\"TestimonialsSection.useEffect\"];\n        }\n    }[\"TestimonialsSection.useEffect\"], [\n        testimonials.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `${(_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialsSection)} ${className || ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().sectionHeader),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"What Our Clients Say\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Hear from professionals who trust Cast Stone for their projects\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialsCarousel),\n                children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialSlide)} ${index === currentTestimonial ? (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialCard),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialContent),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().stars),\n                                        children: [\n                                            ...Array(testimonial.rating)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().star),\n                                                children: \"★\"\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialText),\n                                        children: [\n                                            '\"',\n                                            testimonial.text,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialAuthor),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: testimonial.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: testimonial.company\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this)\n                    }, testimonial.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialControls),\n                children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: `${(_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialDot)} ${index === currentTestimonial ? (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().activeDot) : ''}`,\n                        onClick: ()=>setCurrentTestimonial(index)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/index.ts":
/*!*********************************!*\
  !*** ./src/components/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddToCartButton: () => (/* reexport safe */ _cart_AddToCartButton__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   CartIcon: () => (/* reexport safe */ _cart_CartIcon__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   CartSidebar: () => (/* reexport safe */ _cart_CartSidebar__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   CatalogSection: () => (/* reexport safe */ _home_CatalogSection__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   CollectionsGrid: () => (/* reexport safe */ _home_CollectionsGrid__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   FeaturedCollections: () => (/* reexport safe */ _home_FeaturedCollections__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Footer: () => (/* reexport safe */ _layout_Footer__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   HeroSection: () => (/* reexport safe */ _home_HeroSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   HomePage: () => (/* reexport safe */ _home_HomePage__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Navigation: () => (/* reexport safe */ _layout_Navigation__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   TestimonialsSection: () => (/* reexport safe */ _home_TestimonialsSection__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _layout_Navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./layout/Navigation */ \"(ssr)/./src/components/layout/Navigation.tsx\");\n/* harmony import */ var _layout_Footer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./layout/Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _home_HomePage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _home_HeroSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _home_CollectionsGrid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _home_CatalogSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _home_FeaturedCollections__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _home_TestimonialsSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _cart_CartIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./cart/CartIcon */ \"(ssr)/./src/components/cart/CartIcon.tsx\");\n/* harmony import */ var _cart_CartSidebar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./cart/CartSidebar */ \"(ssr)/./src/components/cart/CartSidebar.tsx\");\n/* harmony import */ var _cart_AddToCartButton__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./cart/AddToCartButton */ \"(ssr)/./src/components/cart/AddToCartButton.tsx\");\n// Layout Components\n\n\n// Home Page Components\n\n\n\n\n\n\n// Cart Components\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsb0JBQW9CO0FBQ3dDO0FBQ1I7QUFFcEQsdUJBQXVCO0FBQytCO0FBQ007QUFDUTtBQUNGO0FBQ1U7QUFDQTtBQUU1RSxrQkFBa0I7QUFDb0M7QUFDTTtBQUNRIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIExheW91dCBDb21wb25lbnRzXG5leHBvcnQgeyBkZWZhdWx0IGFzIE5hdmlnYXRpb24gfSBmcm9tICcuL2xheW91dC9OYXZpZ2F0aW9uJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRm9vdGVyIH0gZnJvbSAnLi9sYXlvdXQvRm9vdGVyJztcblxuLy8gSG9tZSBQYWdlIENvbXBvbmVudHNcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSG9tZVBhZ2UgfSBmcm9tICcuL2hvbWUvSG9tZVBhZ2UnO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBIZXJvU2VjdGlvbiB9IGZyb20gJy4vaG9tZS9IZXJvU2VjdGlvbic7XG5leHBvcnQgeyBkZWZhdWx0IGFzIENvbGxlY3Rpb25zR3JpZCB9IGZyb20gJy4vaG9tZS9Db2xsZWN0aW9uc0dyaWQnO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYXRhbG9nU2VjdGlvbiB9IGZyb20gJy4vaG9tZS9DYXRhbG9nU2VjdGlvbic7XG5leHBvcnQgeyBkZWZhdWx0IGFzIEZlYXR1cmVkQ29sbGVjdGlvbnMgfSBmcm9tICcuL2hvbWUvRmVhdHVyZWRDb2xsZWN0aW9ucyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIFRlc3RpbW9uaWFsc1NlY3Rpb24gfSBmcm9tICcuL2hvbWUvVGVzdGltb25pYWxzU2VjdGlvbic7XG5cbi8vIENhcnQgQ29tcG9uZW50c1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYXJ0SWNvbiB9IGZyb20gJy4vY2FydC9DYXJ0SWNvbic7XG5leHBvcnQgeyBkZWZhdWx0IGFzIENhcnRTaWRlYmFyIH0gZnJvbSAnLi9jYXJ0L0NhcnRTaWRlYmFyJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQWRkVG9DYXJ0QnV0dG9uIH0gZnJvbSAnLi9jYXJ0L0FkZFRvQ2FydEJ1dHRvbic7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIk5hdmlnYXRpb24iLCJGb290ZXIiLCJIb21lUGFnZSIsIkhlcm9TZWN0aW9uIiwiQ29sbGVjdGlvbnNHcmlkIiwiQ2F0YWxvZ1NlY3Rpb24iLCJGZWF0dXJlZENvbGxlY3Rpb25zIiwiVGVzdGltb25pYWxzU2VjdGlvbiIsIkNhcnRJY29uIiwiQ2FydFNpZGViYXIiLCJBZGRUb0NhcnRCdXR0b24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.module.css":
/*!*************************************************!*\
  !*** ./src/components/layout/Footer.module.css ***!
  \*************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"footer\": \"Footer_footer__eNA9m\",\n\t\"footerContent\": \"Footer_footerContent__7IEzx\",\n\t\"footerSection\": \"Footer_footerSection__QRm_X\",\n\t\"footerBottom\": \"Footer_footerBottom__BDIjN\"\n};\n\nmodule.exports.__checksum = \"f4aa87e8bd8d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcRm9vdGVyLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiZm9vdGVyXCI6IFwiRm9vdGVyX2Zvb3Rlcl9fZU5BOW1cIixcblx0XCJmb290ZXJDb250ZW50XCI6IFwiRm9vdGVyX2Zvb3RlckNvbnRlbnRfXzdJRXp4XCIsXG5cdFwiZm9vdGVyU2VjdGlvblwiOiBcIkZvb3Rlcl9mb290ZXJTZWN0aW9uX19RUm1fWFwiLFxuXHRcImZvb3RlckJvdHRvbVwiOiBcIkZvb3Rlcl9mb290ZXJCb3R0b21fX0JESWpOXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImY0YWE4N2U4YmQ4ZFwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Footer_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Footer.module.css */ \"(ssr)/./src/components/layout/Footer.module.css\");\n/* harmony import */ var _Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Footer_module_css__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Footer({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: `${(_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footer)} ${className || ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerContent),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Cast Stone\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Creating timeless beauty with handcrafted cast stone elements for over 25 years.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Company\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/contact\",\n                                            children: \"Contact Us\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/about\",\n                                            children: \"Our Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/retail-locator\",\n                                            children: \"Retail Locator\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/wholesale-signup\",\n                                            children: \"Wholesale Sign-up\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/products/architectural\",\n                                            children: \"Architectural Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/products/designer\",\n                                            children: \"Designer Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/products/limited-edition\",\n                                            children: \"Limited Edition\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/products/sealers\",\n                                            children: \"Cast Stone Sealers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Discover\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/catalog\",\n                                            children: \"Catalog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/collections\",\n                                            children: \"Collections\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/projects\",\n                                            children: \"Completed Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/videos\",\n                                            children: \"Videos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/faqs\",\n                                            children: \"FAQs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Contact Info\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"123 Artisan Way\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 29\n                                    }, this),\n                                    \"Craftsman City, CC 12345\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Phone: (*************\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Email: <EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerBottom),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"\\xa9 2024 Cast Stone Interiors. All rights reserved.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navigation.module.css":
/*!*****************************************************!*\
  !*** ./src/components/layout/Navigation.module.css ***!
  \*****************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"navigation\": \"Navigation_navigation__Z9RLH\",\n\t\"navContainer\": \"Navigation_navContainer__B3qJN\",\n\t\"navMenuWrapper\": \"Navigation_navMenuWrapper__HsfSF\",\n\t\"cartWrapper\": \"Navigation_cartWrapper__L9LAv\",\n\t\"logo\": \"Navigation_logo__hOIJ2\",\n\t\"navMenu\": \"Navigation_navMenu__KOB3_\",\n\t\"dropdown\": \"Navigation_dropdown__yKMbf\",\n\t\"dropdownToggle\": \"Navigation_dropdownToggle__RrJpI\",\n\t\"dropdownMenu\": \"Navigation_dropdownMenu__ZGhNW\",\n\t\"mobileMenuToggle\": \"Navigation_mobileMenuToggle__68btg\",\n\t\"hamburgerLine\": \"Navigation_hamburgerLine__4Hjy7\",\n\t\"active\": \"Navigation_active__Kj1W7\",\n\t\"mobileMenu\": \"Navigation_mobileMenu__rCiva\",\n\t\"mobileNavMenu\": \"Navigation_mobileNavMenu__r9Kw5\",\n\t\"mobileDropdownToggle\": \"Navigation_mobileDropdownToggle__wfkhE\",\n\t\"mobileDropdownMenu\": \"Navigation_mobileDropdownMenu__g5Mxu\"\n};\n\nmodule.exports.__checksum = \"ac0acbd89792\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvTmF2aWdhdGlvbi5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVbWVyIEZhcm9vcVxcRGVza3RvcFxcUGF0cmlja3Mgd2ViXFxDYXN0LVN0b25lXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxsYXlvdXRcXE5hdmlnYXRpb24ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJuYXZpZ2F0aW9uXCI6IFwiTmF2aWdhdGlvbl9uYXZpZ2F0aW9uX19aOVJMSFwiLFxuXHRcIm5hdkNvbnRhaW5lclwiOiBcIk5hdmlnYXRpb25fbmF2Q29udGFpbmVyX19CM3FKTlwiLFxuXHRcIm5hdk1lbnVXcmFwcGVyXCI6IFwiTmF2aWdhdGlvbl9uYXZNZW51V3JhcHBlcl9fSHNmU0ZcIixcblx0XCJjYXJ0V3JhcHBlclwiOiBcIk5hdmlnYXRpb25fY2FydFdyYXBwZXJfX0w5TEF2XCIsXG5cdFwibG9nb1wiOiBcIk5hdmlnYXRpb25fbG9nb19faE9JSjJcIixcblx0XCJuYXZNZW51XCI6IFwiTmF2aWdhdGlvbl9uYXZNZW51X19LT0IzX1wiLFxuXHRcImRyb3Bkb3duXCI6IFwiTmF2aWdhdGlvbl9kcm9wZG93bl9feUtNYmZcIixcblx0XCJkcm9wZG93blRvZ2dsZVwiOiBcIk5hdmlnYXRpb25fZHJvcGRvd25Ub2dnbGVfX1JySnBJXCIsXG5cdFwiZHJvcGRvd25NZW51XCI6IFwiTmF2aWdhdGlvbl9kcm9wZG93bk1lbnVfX1pHaE5XXCIsXG5cdFwibW9iaWxlTWVudVRvZ2dsZVwiOiBcIk5hdmlnYXRpb25fbW9iaWxlTWVudVRvZ2dsZV9fNjhidGdcIixcblx0XCJoYW1idXJnZXJMaW5lXCI6IFwiTmF2aWdhdGlvbl9oYW1idXJnZXJMaW5lX180SGp5N1wiLFxuXHRcImFjdGl2ZVwiOiBcIk5hdmlnYXRpb25fYWN0aXZlX19LajFXN1wiLFxuXHRcIm1vYmlsZU1lbnVcIjogXCJOYXZpZ2F0aW9uX21vYmlsZU1lbnVfX3JDaXZhXCIsXG5cdFwibW9iaWxlTmF2TWVudVwiOiBcIk5hdmlnYXRpb25fbW9iaWxlTmF2TWVudV9fcjlLdzVcIixcblx0XCJtb2JpbGVEcm9wZG93blRvZ2dsZVwiOiBcIk5hdmlnYXRpb25fbW9iaWxlRHJvcGRvd25Ub2dnbGVfX3dma2hFXCIsXG5cdFwibW9iaWxlRHJvcGRvd25NZW51XCI6IFwiTmF2aWdhdGlvbl9tb2JpbGVEcm9wZG93bk1lbnVfX2c1TXh1XCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImFjMGFjYmQ4OTc5MlwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navigation.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navigation_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navigation.module.css */ \"(ssr)/./src/components/layout/Navigation.module.css\");\n/* harmony import */ var _Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../index */ \"(ssr)/./src/components/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Navigation({ className }) {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileDropdowns, setMobileDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        company: false,\n        products: false,\n        discover: false\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navigation)} ${className || ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().logo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: \"Cast Stone\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Interiors & Decorations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navMenuWrapper),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navMenu),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownToggle),\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/contact\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 33,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 33,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/about\",\n                                                        children: \"Our Story\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 34,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/retail-locator\",\n                                                        children: \"Retail Locator\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 35,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/wholesale-signup\",\n                                                        children: \"Wholesale Sign-up\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 36,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownToggle),\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/architectural\",\n                                                        children: \"Architectural Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 42,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/designer\",\n                                                        children: \"Designer Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 43,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/limited-edition\",\n                                                        children: \"Limited Edition Designs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 44,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/sealers\",\n                                                        children: \"Cast Stone Sealers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 45,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/collections\",\n                                        children: \"Collections\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/projects\",\n                                        children: \"Completed Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownToggle),\n                                            children: \"Discover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/catalog\",\n                                                        children: \"Catalog\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/finishes\",\n                                                        children: \"Finishes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 54,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/videos\",\n                                                        children: \"Videos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 55,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/technical\",\n                                                        children: \"Technical Info\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 56,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/faqs\",\n                                                        children: \"FAQs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 57,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().cartWrapper),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_3__.CartIcon, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileMenuToggle)} ${mobileMenuOpen ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileMenu)} ${mobileMenuOpen ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileMenuLogo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: \"Cast Stone\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Interiors & Decorations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileNavMenu),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownToggle)} ${mobileDropdowns.company ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                        onClick: ()=>setMobileDropdowns((prev)=>({\n                                                    ...prev,\n                                                    company: !prev.company\n                                                })),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownMenu)} ${mobileDropdowns.company ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/contact\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Contact Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/about\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Our Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/retail-locator\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Retail Locator\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/wholesale-signup\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Wholesale Sign-up\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownToggle)} ${mobileDropdowns.products ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                        onClick: ()=>setMobileDropdowns((prev)=>({\n                                                    ...prev,\n                                                    products: !prev.products\n                                                })),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownMenu)} ${mobileDropdowns.products ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/architectural\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Architectural Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/designer\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Designer Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/limited-edition\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Limited Edition Designs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/sealers\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Cast Stone Sealers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/collections\",\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: \"Collections\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/projects\",\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: \"Completed Projects\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownToggle)} ${mobileDropdowns.discover ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                        onClick: ()=>setMobileDropdowns((prev)=>({\n                                                    ...prev,\n                                                    discover: !prev.discover\n                                                })),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Discover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownMenu)} ${mobileDropdowns.discover ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/catalog\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Catalog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/finishes\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Finishes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/videos\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Videos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/technical\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Technical Info\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/faqs\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"FAQs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_3__.CartSidebar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/store/cartStore.ts":
/*!********************************!*\
  !*** ./src/store/cartStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getCartItemCount: () => (/* binding */ getCartItemCount),\n/* harmony export */   getCartTotal: () => (/* binding */ getCartTotal),\n/* harmony export */   useCartStore: () => (/* binding */ useCartStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/../node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/../node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\nconst TAX_RATE = 0.08; // 8% tax rate\nconst SHIPPING_RATE = 15.00; // Flat shipping rate\nconst FREE_SHIPPING_THRESHOLD = 500; // Free shipping over $500\nconst useCartStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        // Initial state\n        items: [],\n        isOpen: false,\n        subtotal: 0,\n        tax: 0,\n        shipping: 0,\n        total: 0,\n        shippingInfo: null,\n        isLoading: false,\n        isProcessingPayment: false,\n        // Add item to cart\n        addItem: (newItem)=>{\n            const { items } = get();\n            const existingItem = items.find((item)=>item.id === newItem.id);\n            if (existingItem) {\n                // Update quantity if item already exists\n                get().updateQuantity(existingItem.id, existingItem.quantity + 1);\n            } else {\n                // Add new item\n                set((state)=>({\n                        items: [\n                            ...state.items,\n                            {\n                                ...newItem,\n                                quantity: 1\n                            }\n                        ]\n                    }));\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].success(`${newItem.name} added to cart`);\n            }\n            get().calculateTotals();\n        },\n        // Remove item from cart\n        removeItem: (id)=>{\n            const { items } = get();\n            const item = items.find((item)=>item.id === id);\n            set((state)=>({\n                    items: state.items.filter((item)=>item.id !== id)\n                }));\n            if (item) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].success(`${item.name} removed from cart`);\n            }\n            get().calculateTotals();\n        },\n        // Update item quantity\n        updateQuantity: (id, quantity)=>{\n            if (quantity <= 0) {\n                get().removeItem(id);\n                return;\n            }\n            set((state)=>({\n                    items: state.items.map((item)=>item.id === id ? {\n                            ...item,\n                            quantity\n                        } : item)\n                }));\n            get().calculateTotals();\n        },\n        // Clear entire cart\n        clearCart: ()=>{\n            set({\n                items: [],\n                subtotal: 0,\n                tax: 0,\n                shipping: 0,\n                total: 0\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].success('Cart cleared');\n        },\n        // Toggle cart visibility\n        toggleCart: ()=>{\n            set((state)=>({\n                    isOpen: !state.isOpen\n                }));\n        },\n        // Set shipping information\n        setShippingInfo: (info)=>{\n            set({\n                shippingInfo: info\n            });\n        },\n        // Calculate totals\n        calculateTotals: ()=>{\n            const { items } = get();\n            const subtotal = items.reduce((sum, item)=>sum + item.price * item.quantity, 0);\n            const tax = subtotal * TAX_RATE;\n            const shipping = subtotal >= FREE_SHIPPING_THRESHOLD ? 0 : SHIPPING_RATE;\n            const total = subtotal + tax + shipping;\n            set({\n                subtotal: Number(subtotal.toFixed(2)),\n                tax: Number(tax.toFixed(2)),\n                shipping: Number(shipping.toFixed(2)),\n                total: Number(total.toFixed(2))\n            });\n        },\n        // Set loading state\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        // Set payment processing state\n        setProcessingPayment: (processing)=>{\n            set({\n                isProcessingPayment: processing\n            });\n        }\n    }), {\n    name: 'cast-stone-cart',\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            items: state.items,\n            shippingInfo: state.shippingInfo\n        })\n}));\n// Utility functions\nconst formatPrice = (price)=>{\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n    }).format(price);\n};\nconst getCartItemCount = ()=>{\n    const { items } = useCartStore.getState();\n    return items.reduce((count, item)=>count + item.quantity, 0);\n};\nconst getCartTotal = ()=>{\n    const { total } = useCartStore.getState();\n    return total;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/cartStore.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/zustand","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();