{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcrypt": "^5.1.1", "cloudinary": "^2.6.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "flask": "^0.2.10", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.1", "nodemon": "^3.1.10", "otp-generator": "^4.0.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "sharp": "^0.34.1"}}