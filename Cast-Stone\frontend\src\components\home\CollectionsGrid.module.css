/* Collections Grid - Porsche Style */
.collectionsSection {
  padding: 4rem 2rem;
  background: #f5f5f5;
}

.collectionsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.collectionCard {
  position: relative;
  aspect-ratio: 4/3;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.collectionCard:hover {
  transform: translateY(-5px);
}

.collectionImage {
  position: relative;
  width: 100%;
  height: 100%;
}

.collectionImg {
  object-fit: cover;
  transition: transform 0.3s ease;
}

.collectionCard:hover .collectionImg {
  transform: scale(1.05);
}

.collectionOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 3rem 2rem 2rem;
}

.collectionBrand {
  font-size: 3rem;
  font-weight: 300;
  letter-spacing: 2px;
  margin-bottom: 0.5rem;
  font-family: 'Helvetica Neue', sans-serif;
}

.collectionSubtitle {
  font-size: 1rem;
  font-weight: 400;
  margin-bottom: 1rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.collectionDescription {
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.collectionPrice {
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.collectionButtons {
  display: flex;
  gap: 1rem;
}

.buildBtn,
.allModelsBtn {
  padding: 0.5rem 1rem;
  border: 1px solid white;
  background: transparent;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.buildBtn:hover,
.allModelsBtn:hover {
  background: white;
  color: black;
}

/* Responsive Design */
@media (max-width: 768px) {
  .collectionsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .collectionCard {
    aspect-ratio: 16/9;
  }

  .collectionOverlay {
    padding: 2rem 1.5rem 1.5rem;
  }

  .collectionBrand {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .collectionsSection {
    padding: 2rem 1rem;
  }

  .collectionOverlay {
    padding: 1.5rem 1rem 1rem;
  }

  .collectionBrand {
    font-size: 1.5rem;
  }

  .collectionSubtitle {
    font-size: 0.8rem;
  }

  .collectionDescription {
    font-size: 0.8rem;
  }

  .collectionButtons {
    flex-direction: column;
    gap: 0.5rem;
  }
}
