/* About Page Styles */

.container {
  min-height: 100vh;
  background: var(--background-light);
  color: var(--text-dark);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Navigation */
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(139, 69, 19, 0.1);
}

.navContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  letter-spacing: -0.02em;
}

.logo span {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 400;
}

.navMenu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.navMenu a {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.navMenu a:hover,
.navMenu a.active {
  color: var(--primary-color);
}

.navMenu a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.navMenu a:hover::after,
.navMenu a.active::after {
  width: 100%;
}

/* Hero Section */
.hero {
  padding: 8rem 2rem 4rem;
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
}

.heroSubtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  line-height: 1.7;
}

/* Story Section */
.storySection {
  padding: 6rem 2rem;
  background: white;
}

.storyContainer {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

@media (max-width: 768px) {
  .storyContainer {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

.storyContent h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 2rem;
}

.storyContent p {
  font-size: 1.1rem;
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.storyImage {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.storyImg {
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* Section Headers */
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.sectionHeader h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.sectionHeader p {
  font-size: 1.1rem;
  color: var(--text-light);
}

/* Values Section */
.valuesSection {
  padding: 6rem 2rem;
  background: var(--background-light);
}

.valuesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.valueCard {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.valueCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
}

.valueIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.valueCard h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.valueCard p {
  color: var(--text-light);
  line-height: 1.6;
}

/* Team Section */
.teamSection {
  padding: 6rem 2rem;
  background: white;
}

.teamGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.teamMember {
  text-align: center;
  background: var(--background-light);
  padding: 2rem;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.teamMember:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow);
}

.teamPhoto {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 1.5rem;
  border: 4px solid var(--primary-color);
}

.teamMember h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.teamRole {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.teamMember p:not(.teamRole) {
  color: var(--text-light);
  line-height: 1.6;
}

/* CTA Section */
.ctaSection {
  padding: 4rem 2rem;
  background: var(--primary-color);
  color: white;
  text-align: center;
}

.ctaContent h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.ctaContent p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.ctaBtn {
  padding: 1rem 2rem;
  background: white;
  color: var(--primary-color);
  border: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ctaBtn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Footer */
.footer {
  background: var(--text-dark);
  color: white;
  padding: 3rem 2rem 1rem;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footerSection h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footerSection h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footerSection p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.footerSection ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerSection ul li {
  margin-bottom: 0.5rem;
}

.footerSection ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footerSection ul li a:hover {
  color: var(--secondary-color);
}

.footerBottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Mobile Hamburger Menu */
.mobileMenuToggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.hamburgerLine {
  width: 25px;
  height: 3px;
  background: var(--text-dark);
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobileMenuToggle.active .hamburgerLine:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobileMenuToggle.active .hamburgerLine:nth-child(2) {
  opacity: 0;
}

.mobileMenuToggle.active .hamburgerLine:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.mobileMenu {
  position: fixed;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100vh;
  background: white;
  z-index: 1000;
  transition: left 0.3s ease;
  padding-top: 80px;
  overflow-y: auto;
}

.mobileMenu.active {
  left: 0;
}

.mobileNavMenu {
  list-style: none;
  padding: 2rem;
  margin: 0;
}

.mobileNavMenu > li {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--background-light);
  padding-bottom: 1rem;
}

.mobileNavMenu a {
  display: block;
  padding: 1rem 0;
  color: var(--text-dark);
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 500;
}

.mobileDropdownToggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.mobileDropdownToggle::after {
  content: '+';
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.mobileDropdownToggle.active::after {
  transform: rotate(45deg);
}

.mobileDropdownMenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: var(--background-light);
  margin-top: 1rem;
  border-radius: 8px;
}

.mobileDropdownMenu.active {
  max-height: 300px;
}

.mobileDropdownMenu li {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.mobileDropdownMenu a {
  padding: 0.75rem 1rem;
  font-size: 1rem;
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Navigation */
  .navMenu {
    display: none;
  }

  .mobileMenuToggle {
    display: flex;
  }

  .navContainer {
    padding: 0 1rem;
  }

  /* Hero Section */
  .heroTitle {
    font-size: 2.5rem;
  }

  /* About Section */
  .aboutGrid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .aboutSection {
    padding: 4rem 1rem;
  }

  /* Values Section */
  .valuesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .valuesSection {
    padding: 4rem 1rem;
  }

  /* Team Section */
  .teamGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .teamSection {
    padding: 4rem 1rem;
  }

  .storyContent h2 {
    font-size: 2rem;
  }

  /* Footer */
  .footerContent {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }

  .hero {
    padding: 6rem 1rem 3rem;
  }

  .aboutSection,
  .valuesSection,
  .teamSection {
    padding: 3rem 1rem;
  }

  .valueCard {
    padding: 1.5rem;
  }

  .teamCard {
    padding: 1.5rem;
  }
}
