/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/layout/Navigation.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/* Top Navigation Bar */
.Navigation_navigation__Z9RLH {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(139, 69, 19, 0.1);
}

 /* .navContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem; 
  display: flex;
  justify-content: space-between;
  flex-direction: column; 
    justify-content: center;
  align-items: center;
}  */

.Navigation_navContainer__B3qJN {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between; /* Spread logo and nav items */
  align-items: center;
}

.Navigation_navMenuWrapper__HsfSF {
  flex: 1;
  display: flex;
  justify-content: center;
}

.Navigation_cartWrapper__L9LAv {
  display: flex;
  align-items: center;
  margin-left: 1rem;
}

.Navigation_logo__hOIJ2 h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  letter-spacing: -0.02em;
}

.Navigation_logo__hOIJ2 span {
  display: block;
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 400;
}

.Navigation_navMenu__KOB3_ {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
  align-items: center;
}

.Navigation_navMenu__KOB3_ > li {
  position: relative;
}

.Navigation_navMenu__KOB3_ a {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
  padding: 0.5rem 0;
}

.Navigation_navMenu__KOB3_ a:hover {
  color: var(--primary-color);
}

/* Dropdown Styles */
.Navigation_dropdown__yKMbf {
  position: relative;
}

.Navigation_dropdownToggle__RrJpI::after {
  content: '▼';
  font-size: 0.7rem;
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.Navigation_dropdown__yKMbf:hover .Navigation_dropdownToggle__RrJpI::after {
  transform: rotate(180deg);
}

.Navigation_dropdownMenu__ZGhNW {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  min-width: 200px;
  box-shadow: var(--shadow);
  border-radius: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  list-style: none;
  margin: 0;
}

.Navigation_dropdown__yKMbf:hover .Navigation_dropdownMenu__ZGhNW {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.Navigation_dropdownMenu__ZGhNW li {
  margin: 0;
}

.Navigation_dropdownMenu__ZGhNW a {
  display: block;
  padding: 0.75rem 1.5rem;
  color: var(--text-dark);
  text-decoration: none;
  transition: background-color 0.3s ease;
  font-weight: 400;
}

.Navigation_dropdownMenu__ZGhNW a:hover {
  background-color: var(--background-light);
  color: var(--primary-color);
}

/* Mobile Hamburger Menu */
.Navigation_mobileMenuToggle__68btg {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.Navigation_hamburgerLine__4Hjy7 {
  width: 25px;
  height: 3px;
  background-color: var(--text-dark);
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.Navigation_mobileMenuToggle__68btg.Navigation_active__Kj1W7 .Navigation_hamburgerLine__4Hjy7:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.Navigation_mobileMenuToggle__68btg.Navigation_active__Kj1W7 .Navigation_hamburgerLine__4Hjy7:nth-child(2) {
  opacity: 0;
}

.Navigation_mobileMenuToggle__68btg.Navigation_active__Kj1W7 .Navigation_hamburgerLine__4Hjy7:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.Navigation_mobileMenu__rCiva {
  position: fixed;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100vh;
  background: white;
  z-index: 999;
  transition: left 0.3s ease;
  padding-top: 80px;
  overflow-y: auto;
}

.Navigation_mobileMenu__rCiva.Navigation_active__Kj1W7 {
  left: 0;
}

.Navigation_mobileNavMenu__r9Kw5 {
  list-style: none;
  padding: 2rem;
  margin: 0;
}

.Navigation_mobileNavMenu__r9Kw5 > li {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--background-light);
  padding-bottom: 1rem;
}

.Navigation_mobileNavMenu__r9Kw5 a {
  display: block;
  padding: 1rem 0;
  color: var(--text-dark);
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 500;
}

.Navigation_mobileDropdownToggle__wfkhE {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.Navigation_mobileDropdownToggle__wfkhE::after {
  content: '+';
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.Navigation_mobileDropdownToggle__wfkhE.Navigation_active__Kj1W7::after {
  transform: rotate(45deg);
}

.Navigation_mobileDropdownMenu__g5Mxu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: var(--background-light);
  margin-top: 1rem;
  border-radius: 8px;
}

.Navigation_mobileDropdownMenu__g5Mxu.Navigation_active__Kj1W7 {
  max-height: 300px;
}

.Navigation_mobileDropdownMenu__g5Mxu li {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.Navigation_mobileDropdownMenu__g5Mxu a {
  padding: 0.75rem 1rem;
  font-size: 1rem;
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .Navigation_navMenu__KOB3_ {
    display: none;
  }

  .Navigation_mobileMenuToggle__68btg {
    display: flex;
  }

  .Navigation_navContainer__B3qJN {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .Navigation_navMenu__KOB3_ {
    display: none;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/layout/Footer.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/* Footer */
.Footer_footer__eNA9m {
  background: var(--text-dark);
  color: white;
  padding: 4rem 2rem 2rem;
}

.Footer_footerContent__7IEzx {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-bottom: 2rem;
}

.Footer_footerSection__QRm_X h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.Footer_footerSection__QRm_X h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.Footer_footerSection__QRm_X p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.Footer_footerSection__QRm_X ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.Footer_footerSection__QRm_X ul li {
  margin-bottom: 0.5rem;
}

.Footer_footerSection__QRm_X ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.Footer_footerSection__QRm_X ul li a:hover {
  color: var(--secondary-color);
}

.Footer_footerBottom__BDIjN {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .Footer_footerContent__7IEzx {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/home/<USER>
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/* HomePage Container */
.HomePage_container__GVF35 {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/home/<USER>
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/* Hero Section with Video Background */
.HeroSection_hero__tVlJy {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.HeroSection_videoBackground__1b56U {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.HeroSection_heroVideo__wmHr5 {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.HeroSection_videoOverlay__GbFoP {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 2;
}

.HeroSection_heroContent__VYZbs {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.HeroSection_heroTitle__nnst_ {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.HeroSection_highlight__Wgy18 {
  color: var(--secondary-color);
}

.HeroSection_heroSubtitle__J_PUa {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.HeroSection_heroActions__KheHS {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.HeroSection_primaryBtn__ajxDg {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.HeroSection_primaryBtn__ajxDg:hover {
  background: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.HeroSection_secondaryBtn__dFomW {
  background: transparent;
  color: white;
  border: 2px solid white;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.HeroSection_secondaryBtn__dFomW:hover {
  background: white;
  color: var(--primary-color);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .HeroSection_heroTitle__nnst_ {
    font-size: 2.5rem;
  }

  .HeroSection_heroSubtitle__J_PUa {
    font-size: 1.1rem;
  }

  .HeroSection_heroActions__KheHS {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .HeroSection_heroTitle__nnst_ {
    font-size: 2rem;
  }

  .HeroSection_heroContent__VYZbs {
    padding: 0 1rem;
  }

  .HeroSection_primaryBtn__ajxDg,
  .HeroSection_secondaryBtn__dFomW {
    width: 100%;
    max-width: 280px;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/home/<USER>
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/* Collections Grid - Porsche Style */
.CollectionsGrid_collectionsSection__INn5d {
  padding: 4rem 2rem;
  background: #f5f5f5;
}

.CollectionsGrid_collectionsGrid__B6kkM {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.CollectionsGrid_collectionCard__qG8Ub {
  position: relative;
  aspect-ratio: 4/3;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.CollectionsGrid_collectionCard__qG8Ub:hover {
  transform: translateY(-5px);
}

.CollectionsGrid_collectionImage__Jw6QX {
  position: relative;
  width: 100%;
  height: 100%;
}

.CollectionsGrid_collectionImg__QDbrX {
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.CollectionsGrid_collectionCard__qG8Ub:hover .CollectionsGrid_collectionImg__QDbrX {
  transform: scale(1.05);
}

.CollectionsGrid_collectionOverlay__jJQyH {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 3rem 2rem 2rem;
}

.CollectionsGrid_collectionBrand__16gRy {
  font-size: 3rem;
  font-weight: 300;
  letter-spacing: 2px;
  margin-bottom: 0.5rem;
  font-family: 'Helvetica Neue', sans-serif;
}

.CollectionsGrid_collectionSubtitle__6kv2c {
  font-size: 1rem;
  font-weight: 400;
  margin-bottom: 1rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.CollectionsGrid_collectionDescription__yFGMj {
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.CollectionsGrid_collectionPrice__OeY5J {
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.CollectionsGrid_collectionButtons__Xe7JP {
  display: flex;
  gap: 1rem;
}

.CollectionsGrid_buildBtn__6ctOy,
.CollectionsGrid_allModelsBtn__qX2X4 {
  padding: 0.5rem 1rem;
  border: 1px solid white;
  background: transparent;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.CollectionsGrid_buildBtn__6ctOy:hover,
.CollectionsGrid_allModelsBtn__qX2X4:hover {
  background: white;
  color: black;
}

/* Responsive Design */
@media (max-width: 768px) {
  .CollectionsGrid_collectionsGrid__B6kkM {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .CollectionsGrid_collectionCard__qG8Ub {
    aspect-ratio: 16/9;
  }

  .CollectionsGrid_collectionOverlay__jJQyH {
    padding: 2rem 1.5rem 1.5rem;
  }

  .CollectionsGrid_collectionBrand__16gRy {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .CollectionsGrid_collectionsSection__INn5d {
    padding: 2rem 1rem;
  }

  .CollectionsGrid_collectionOverlay__jJQyH {
    padding: 1.5rem 1rem 1rem;
  }

  .CollectionsGrid_collectionBrand__16gRy {
    font-size: 1.5rem;
  }

  .CollectionsGrid_collectionSubtitle__6kv2c {
    font-size: 0.8rem;
  }

  .CollectionsGrid_collectionDescription__yFGMj {
    font-size: 0.8rem;
  }

  .CollectionsGrid_collectionButtons__Xe7JP {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/home/<USER>
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/* Online Catalog Section */
.CatalogSection_catalogSection__xWHSY {
  padding: 6rem 2rem;
  background: var(--primary-color);
  color: white;
}

.CatalogSection_catalogContainer__pOYTt {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.CatalogSection_catalogContent__Ut8dn {
  padding: 2rem;
}

.CatalogSection_catalogText__Q2fXT h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: white;
}

.CatalogSection_catalogText__Q2fXT p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.CatalogSection_catalogBtn__M84YU {
  padding: 1rem 2.5rem;
  background: white;
  color: var(--primary-color);
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.CatalogSection_catalogBtn__M84YU:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow);
}

.CatalogSection_catalogImage__mwGOP {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
}

.CatalogSection_catalogImg__Es0x_ {
  width: 100%;
  height: 400px;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.CatalogSection_catalogContainer__pOYTt:hover .CatalogSection_catalogImg__Es0x_ {
  transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
  .CatalogSection_catalogContainer__pOYTt {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .CatalogSection_catalogContent__Ut8dn {
    padding: 1rem;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/home/<USER>
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/* Featured Collections - Goop Style */
.FeaturedCollections_featuredCollections__RJNQj {
  padding: 4rem 2rem;
  background: white;
}

.FeaturedCollections_featuredGrid__EVygU {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

.FeaturedCollections_featuredCard__r7DIE {
  position: relative;
  aspect-ratio: 3/4;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.FeaturedCollections_featuredCard__r7DIE:hover {
  transform: translateY(-5px);
}

.FeaturedCollections_featuredImage__9yjUq {
  position: relative;
  width: 100%;
  height: 70%;
}

.FeaturedCollections_featuredImg__xJy_R {
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.FeaturedCollections_featuredCard__r7DIE:hover .FeaturedCollections_featuredImg__xJy_R {
  transform: scale(1.05);
}

.FeaturedCollections_featuredContent__tM1Tm {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 1.5rem;
  height: 30%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.FeaturedCollections_featuredTitle__7PpZ0 {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.FeaturedCollections_featuredSubtitle__qT5hZ {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-bottom: 1rem;
  line-height: 1.3;
}

.FeaturedCollections_featuredBtn___wxeN {
  padding: 0.5rem 1rem;
  background: transparent;
  color: var(--text-dark);
  border: 1px solid var(--text-dark);
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: center;
}

.FeaturedCollections_featuredBtn___wxeN:hover {
  background: var(--text-dark);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .FeaturedCollections_featuredGrid__EVygU {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .FeaturedCollections_featuredCard__r7DIE {
    aspect-ratio: 1/1.2;
  }

  .FeaturedCollections_featuredContent__tM1Tm {
    padding: 1rem;
  }

  .FeaturedCollections_featuredTitle__7PpZ0 {
    font-size: 0.8rem;
  }

  .FeaturedCollections_featuredSubtitle__qT5hZ {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .FeaturedCollections_featuredCollections__RJNQj {
    padding: 2rem 1rem;
  }

  .FeaturedCollections_featuredGrid__EVygU {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .FeaturedCollections_featuredCard__r7DIE {
    aspect-ratio: 4/3;
  }

  .FeaturedCollections_featuredContent__tM1Tm {
    position: relative;
    height: auto;
    padding: 1.5rem;
  }

  .FeaturedCollections_featuredImage__9yjUq {
    height: 200px;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/home/<USER>
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/* Testimonials Section */
.TestimonialsSection_testimonialsSection__eFqMD {
  padding: 6rem 2rem;
  background: var(--background-light);
}

.TestimonialsSection_sectionHeader__P_QS4 {
  text-align: center;
  margin-bottom: 4rem;
}

.TestimonialsSection_sectionHeader__P_QS4 h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.TestimonialsSection_sectionHeader__P_QS4 p {
  font-size: 1.1rem;
  color: var(--text-light);
}

.TestimonialsSection_testimonialsCarousel__IrvAW {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
}

.TestimonialsSection_testimonialSlide__jWab0 {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.TestimonialsSection_testimonialSlide__jWab0.TestimonialsSection_active__2TCrK {
  display: block;
  opacity: 1;
}

.TestimonialsSection_testimonialCard__g3Le5 {
  background: white;
  padding: 3rem;
  border-radius: 15px;
  box-shadow: var(--shadow);
  text-align: center;
}

.TestimonialsSection_stars__sPqUh {
  margin-bottom: 1.5rem;
}

.TestimonialsSection_star__r9WUB {
  color: #ffd700;
  font-size: 1.5rem;
  margin: 0 0.1rem;
}

.TestimonialsSection_testimonialText__rv9eB {
  font-size: 1.2rem;
  font-style: italic;
  color: var(--text-dark);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.TestimonialsSection_testimonialAuthor__tNCS7 h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.TestimonialsSection_testimonialAuthor__tNCS7 span {
  color: var(--text-light);
  font-size: 0.9rem;
}

.TestimonialsSection_testimonialControls__WZrGJ {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.TestimonialsSection_testimonialDot__WabFU {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(139, 69, 19, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.TestimonialsSection_testimonialDot__WabFU.TestimonialsSection_activeDot__NBmoY {
  background: var(--primary-color);
  transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .TestimonialsSection_sectionHeader__P_QS4 {
    margin-bottom: 2rem;
  }

  .TestimonialsSection_testimonialCard__g3Le5 {
    padding: 2rem;
  }

  .TestimonialsSection_testimonialText__rv9eB {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .TestimonialsSection_testimonialCard__g3Le5 {
    padding: 1.5rem;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/cart/CartIcon.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
.CartIcon_cartIcon__goXOm {
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.CartIcon_cartIcon__goXOm:hover {
  background-color: var(--background-light);
  transform: translateY(-1px);
}

.CartIcon_cartIcon__goXOm:active {
  transform: translateY(0);
}

.CartIcon_iconWrapper__y4SgP {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.CartIcon_icon__c6CEd {
  width: 24px;
  height: 24px;
  color: var(--text-dark);
  transition: color 0.2s ease;
}

.CartIcon_cartIcon__goXOm:hover .CartIcon_icon__c6CEd {
  color: var(--primary-color);
}

.CartIcon_badge__us6B1 {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--cart-error);
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
  padding: 0 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: CartIcon_bounceIn__HWcAh 0.3s ease;
}

@keyframes CartIcon_bounceIn__HWcAh {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .CartIcon_cartIcon__goXOm {
    padding: 6px;
  }
  
  .CartIcon_icon__c6CEd {
    width: 22px;
    height: 22px;
  }
  
  .CartIcon_badge__us6B1 {
    min-width: 18px;
    height: 18px;
    font-size: 11px;
    top: -6px;
    right: -6px;
  }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/cart/CartSidebar.module.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
.CartSidebar_overlay__mqzKG {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--cart-overlay);
  z-index: 998;
  animation: CartSidebar_fadeIn__nqWOk 0.3s ease;
}

.CartSidebar_sidebar__2_A5n {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  max-width: 480px;
  height: 100vh;
  background: var(--cart-bg);
  box-shadow: -4px 0 20px var(--cart-shadow);
  z-index: 999;
  display: flex;
  flex-direction: column;
  animation: CartSidebar_slideIn__pC9qA 0.3s ease;
}

.CartSidebar_header__o1Au0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--cart-border);
  background: var(--white);
}

.CartSidebar_headerContent__EmC2j {
  display: flex;
  align-items: center;
  gap: 12px;
}

.CartSidebar_headerIcon__bLyLa {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
}

.CartSidebar_title__T_BOr {
  font-size: 20px;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0;
}

.CartSidebar_itemCount__KfR58 {
  font-size: 14px;
  color: var(--cart-text-secondary);
}

.CartSidebar_closeButton__8Ydlb {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: var(--cart-text-secondary);
  transition: all 0.2s ease;
}

.CartSidebar_closeButton__8Ydlb:hover {
  background: var(--background-light);
  color: var(--cart-text-primary);
}

.CartSidebar_content__Ru9kP {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.CartSidebar_emptyCart__3D1F3 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
  text-align: center;
}

.CartSidebar_emptyIcon__IhnS7 {
  width: 64px;
  height: 64px;
  color: var(--cart-text-muted);
  margin-bottom: 16px;
}

.CartSidebar_emptyCart__3D1F3 h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0 0 8px 0;
}

.CartSidebar_emptyCart__3D1F3 p {
  color: var(--cart-text-secondary);
  margin: 0 0 24px 0;
}

.CartSidebar_shopButton__jQbqs {
  background: var(--primary-color);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: background 0.2s ease;
}

.CartSidebar_shopButton__jQbqs:hover {
  background: var(--cart-primary-hover);
}

.CartSidebar_items__LfUBW {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
}

.CartSidebar_item__JsbsR {
  display: flex;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid var(--cart-border);
}

.CartSidebar_item__JsbsR:last-child {
  border-bottom: none;
}

.CartSidebar_itemImage__Qm_S7 {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--background-light);
}

.CartSidebar_image__MCno_ {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.CartSidebar_imagePlaceholder__v63ez {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--cart-text-muted);
}

.CartSidebar_itemDetails__q83BZ {
  flex: 1;
  min-width: 0;
}

.CartSidebar_itemName__kbsGh {
  font-size: 16px;
  font-weight: 500;
  color: var(--cart-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.CartSidebar_itemCategory__Vn8aY {
  font-size: 14px;
  color: var(--cart-text-secondary);
  margin: 0 0 8px 0;
}

.CartSidebar_itemPrice__jWgLO {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 12px 0;
}

.CartSidebar_quantityControls__cwH5X {
  display: flex;
  align-items: center;
  gap: 8px;
}

.CartSidebar_quantityButton__rAkZS {
  background: var(--background-light);
  border: 1px solid var(--cart-border);
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.CartSidebar_quantityButton__rAkZS:hover {
  background: var(--cart-border);
}

.CartSidebar_quantityButton__rAkZS svg {
  width: 16px;
  height: 16px;
}

.CartSidebar_quantity__yNcp1 {
  font-weight: 500;
  min-width: 24px;
  text-align: center;
}

.CartSidebar_itemActions__P0Tbw {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.CartSidebar_itemTotal__EXHEC {
  font-size: 16px;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0;
}

.CartSidebar_removeButton__v8Ns5 {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  color: var(--cart-text-muted);
  transition: all 0.2s ease;
}

.CartSidebar_removeButton__v8Ns5:hover {
  background: var(--cart-error);
  color: white;
}

.CartSidebar_removeButton__v8Ns5 svg {
  width: 16px;
  height: 16px;
}

.CartSidebar_summary__oV_5c {
  padding: 20px 24px;
  border-top: 1px solid var(--cart-border);
  background: var(--background-light);
}

.CartSidebar_summaryRow__PZW5e {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.CartSidebar_summaryRow__PZW5e:first-child {
  color: var(--cart-text-secondary);
}

.CartSidebar_summaryTotal__vtFkT {
  display: flex;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 600;
  color: var(--cart-text-primary);
  padding-top: 12px;
  border-top: 1px solid var(--cart-border);
  margin-top: 8px;
}

.CartSidebar_freeShippingNote__knVul {
  font-size: 12px;
  color: var(--cart-success);
  margin: 8px 0 0 0;
  text-align: center;
}

.CartSidebar_actions__PP6QO {
  padding: 20px 24px;
  border-top: 1px solid var(--cart-border);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.CartSidebar_checkoutButton__HuDB4 {
  background: var(--primary-color);
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  transition: background 0.2s ease;
}

.CartSidebar_checkoutButton__HuDB4:hover {
  background: var(--cart-primary-hover);
}

.CartSidebar_viewCartButton__K05HQ {
  background: transparent;
  color: var(--primary-color);
  padding: 12px 24px;
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
}

.CartSidebar_viewCartButton__K05HQ:hover {
  background: var(--primary-color);
  color: white;
}

@keyframes CartSidebar_fadeIn__nqWOk {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes CartSidebar_slideIn__pC9qA {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .CartSidebar_sidebar__2_A5n {
    max-width: 100%;
  }
  
  .CartSidebar_header__o1Au0 {
    padding: 16px 20px;
  }
  
  .CartSidebar_items__LfUBW {
    padding: 12px 20px;
  }
  
  .CartSidebar_summary__oV_5c,
  .CartSidebar_actions__PP6QO {
    padding: 16px 20px;
  }
  
  .CartSidebar_item__JsbsR {
    gap: 12px;
  }
  
  .CartSidebar_itemImage__Qm_S7 {
    width: 60px;
    height: 60px;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/cart/AddToCartButton.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
.AddToCartButton_addToCartButton__UeAA8 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.AddToCartButton_addToCartButton__UeAA8:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Variants */
.AddToCartButton_primary__nObA3 {
  background: var(--primary-color);
  color: white;
}

.AddToCartButton_primary__nObA3:hover:not(:disabled) {
  background: var(--cart-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
}

.AddToCartButton_secondary__2Q9uu {
  background: var(--cart-secondary);
  color: var(--cart-text-primary);
}

.AddToCartButton_secondary__2Q9uu:hover:not(:disabled) {
  background: var(--cart-border);
  transform: translateY(-1px);
}

.AddToCartButton_outline__g35qH {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.AddToCartButton_outline__g35qH:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
}

/* Sizes */
.AddToCartButton_small__y17jf {
  padding: 8px 16px;
  font-size: 14px;
}

.AddToCartButton_small__y17jf svg {
  width: 16px;
  height: 16px;
}

.AddToCartButton_medium__AC18e {
  padding: 12px 24px;
  font-size: 16px;
}

.AddToCartButton_medium__AC18e svg {
  width: 18px;
  height: 18px;
}

.AddToCartButton_large__uuri9 {
  padding: 16px 32px;
  font-size: 18px;
}

.AddToCartButton_large__uuri9 svg {
  width: 20px;
  height: 20px;
}

/* States */
.AddToCartButton_loading__dkmYR {
  pointer-events: none;
}

.AddToCartButton_success__ov0hC {
  background: var(--cart-success) !important;
  color: white !important;
  border-color: var(--cart-success) !important;
}

.AddToCartButton_inCart__BGkau {
  background: var(--cart-text-muted) !important;
  color: white !important;
  border-color: var(--cart-text-muted) !important;
}

.AddToCartButton_disabled__xUbkt {
  background: var(--cart-text-muted) !important;
  color: white !important;
  border-color: var(--cart-text-muted) !important;
}

.AddToCartButton_text__GHUHN {
  white-space: nowrap;
}

.AddToCartButton_spinner__TDX27 {
  animation: AddToCartButton_spin__dsVqO 1s linear infinite;
}

@keyframes AddToCartButton_spin__dsVqO {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Ripple effect */
.AddToCartButton_addToCartButton__UeAA8::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.AddToCartButton_addToCartButton__UeAA8:active::before {
  width: 300px;
  height: 300px;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .AddToCartButton_small__y17jf {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .AddToCartButton_medium__AC18e {
    padding: 10px 20px;
    font-size: 15px;
  }
  
  .AddToCartButton_large__uuri9 {
    padding: 14px 28px;
    font-size: 17px;
  }
}

/* Focus styles for accessibility */
.AddToCartButton_addToCartButton__UeAA8:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.AddToCartButton_addToCartButton__UeAA8:focus:not(:focus-visible) {
  outline: none;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/checkout/CheckoutForm.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
.CheckoutForm_checkoutForm__A5Fkn {
  width: 100%;
}

.CheckoutForm_stepIndicator__yPCl4 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  position: relative;
}

.CheckoutForm_stepWrapper__IrDEz {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 200px;
}

.CheckoutForm_step__wezm9 {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid var(--cart-border);
  background: var(--cart-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 12px;
}

.CheckoutForm_step__wezm9:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.CheckoutForm_step__wezm9.CheckoutForm_active__lmT2o {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

.CheckoutForm_step__wezm9.CheckoutForm_completed__S23xU {
  border-color: var(--cart-success);
  background: var(--cart-success);
  color: white;
}

.CheckoutForm_stepIcon__YixCv {
  width: 24px;
  height: 24px;
}

.CheckoutForm_stepNumber__QdFrN {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: var(--cart-text-primary);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.CheckoutForm_step__wezm9.CheckoutForm_active__lmT2o .CheckoutForm_stepNumber__QdFrN {
  background: white;
  color: var(--primary-color);
}

.CheckoutForm_step__wezm9.CheckoutForm_completed__S23xU .CheckoutForm_stepNumber__QdFrN {
  background: white;
  color: var(--cart-success);
}

.CheckoutForm_stepTitle__RqsGL {
  font-size: 14px;
  font-weight: 500;
  color: var(--cart-text-secondary);
  text-align: center;
  max-width: 120px;
}

.CheckoutForm_stepConnector__LOqYX {
  position: absolute;
  top: 30px;
  left: 50%;
  right: -50%;
  height: 3px;
  background: var(--cart-border);
  z-index: -1;
}

.CheckoutForm_stepConnector__LOqYX.CheckoutForm_completed__S23xU {
  background: var(--cart-success);
}

.CheckoutForm_stepContent__ipXmz {
  background: var(--background-light);
  border-radius: 12px;
  padding: 32px;
}

.CheckoutForm_form__cByJD {
  width: 100%;
}

.CheckoutForm_sectionTitle__t1oMT {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0 0 24px 0;
}

.CheckoutForm_formRow__o_HrD {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.CheckoutForm_formRow__o_HrD:has(.CheckoutForm_formGroup__EBwOK:nth-child(3)) {
  grid-template-columns: 1fr 1fr 1fr;
}

.CheckoutForm_formGroup__EBwOK {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.CheckoutForm_label__Aianr {
  font-size: 14px;
  font-weight: 500;
  color: var(--cart-text-primary);
  margin-bottom: 8px;
}

.CheckoutForm_input__gpSo2 {
  padding: 12px 16px;
  border: 2px solid var(--cart-border);
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  background: var(--cart-bg);
  color: var(--cart-text-primary);
}

.CheckoutForm_input__gpSo2:focus {
  outline: none;
  border-color: var(--primary-color);
}

.CheckoutForm_input__gpSo2.CheckoutForm_error__xJTwE {
  border-color: var(--cart-error);
}

.CheckoutForm_errorMessage__lyG1q {
  color: var(--cart-error);
  font-size: 12px;
  margin-top: 4px;
}

.CheckoutForm_continueButton__gnd_M {
  background: var(--primary-color);
  color: white;
  padding: 16px 32px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  margin-top: 20px;
}

.CheckoutForm_continueButton__gnd_M:hover:not(:disabled) {
  background: var(--cart-primary-hover);
  transform: translateY(-1px);
}

.CheckoutForm_continueButton__gnd_M:disabled {
  background: var(--cart-text-muted);
  cursor: not-allowed;
  transform: none;
}

.CheckoutForm_buttonGroup__8ZIgW {
  display: flex;
  gap: 16px;
  margin-top: 24px;
}

.CheckoutForm_backButton___XQxO {
  background: transparent;
  color: var(--cart-text-secondary);
  padding: 16px 32px;
  border: 2px solid var(--cart-border);
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
}

.CheckoutForm_backButton___XQxO:hover {
  background: var(--cart-border);
  color: var(--cart-text-primary);
}

.CheckoutForm_placeOrderButton__8XcnK {
  background: var(--cart-success);
  color: white;
  padding: 16px 32px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 2;
}

.CheckoutForm_placeOrderButton__8XcnK:hover {
  background: #047857;
  transform: translateY(-1px);
}

.CheckoutForm_paymentSection__ByyXC,
.CheckoutForm_reviewSection__nBCPM {
  text-align: center;
  padding: 40px 20px;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .CheckoutForm_stepIndicator__yPCl4 {
    margin-bottom: 30px;
  }
  
  .CheckoutForm_step__wezm9 {
    width: 50px;
    height: 50px;
  }
  
  .CheckoutForm_stepIcon__YixCv {
    width: 20px;
    height: 20px;
  }
  
  .CheckoutForm_stepNumber__QdFrN {
    width: 18px;
    height: 18px;
    font-size: 11px;
  }
  
  .CheckoutForm_stepTitle__RqsGL {
    font-size: 12px;
    max-width: 100px;
  }
  
  .CheckoutForm_stepContent__ipXmz {
    padding: 24px;
  }
  
  .CheckoutForm_formRow__o_HrD {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .CheckoutForm_formRow__o_HrD:has(.CheckoutForm_formGroup__EBwOK:nth-child(3)) {
    grid-template-columns: 1fr;
  }
  
  .CheckoutForm_buttonGroup__8ZIgW {
    flex-direction: column;
  }
  
  .CheckoutForm_backButton___XQxO,
  .CheckoutForm_continueButton__gnd_M,
  .CheckoutForm_placeOrderButton__8XcnK {
    width: 100%;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/checkout/OrderSummary.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
.OrderSummary_orderSummary__iC9DF {
  width: 100%;
}

.OrderSummary_header__16xFc {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--cart-border);
}

.OrderSummary_title__f634X {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0;
}

.OrderSummary_itemCount__pAXZU {
  font-size: 14px;
  color: var(--cart-text-secondary);
}

.OrderSummary_items__8_QDW {
  margin-bottom: 24px;
}

.OrderSummary_item__1_k6i {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid var(--cart-border);
}

.OrderSummary_item__1_k6i:last-child {
  border-bottom: none;
}

.OrderSummary_itemImage__a51EY {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--background-light);
}

.OrderSummary_image__sVHTR {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.OrderSummary_imagePlaceholder__iSNfz {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--cart-text-muted);
}

.OrderSummary_itemDetails__OQDxs {
  flex: 1;
  min-width: 0;
}

.OrderSummary_itemName__qLipO {
  font-size: 14px;
  font-weight: 500;
  color: var(--cart-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.OrderSummary_itemCategory__bJ_6G {
  font-size: 12px;
  color: var(--cart-text-secondary);
  margin: 0 0 8px 0;
}

.OrderSummary_itemPricing__VYAXu {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.OrderSummary_itemPrice__Lp45h {
  color: var(--primary-color);
  font-weight: 500;
}

.OrderSummary_itemQuantity__xe6Ol {
  color: var(--cart-text-secondary);
}

.OrderSummary_itemTotal__tr7yO {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-text-primary);
  text-align: right;
}

.OrderSummary_pricing__tu4nz {
  padding: 20px 0;
  border-top: 1px solid var(--cart-border);
  margin-bottom: 20px;
}

.OrderSummary_pricingRow__Sju6_ {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  color: var(--cart-text-secondary);
}

.OrderSummary_pricingRow__Sju6_:last-child {
  margin-bottom: 0;
}

.OrderSummary_freeShippingNote__e05vI {
  background: var(--background-light);
  padding: 12px;
  border-radius: 6px;
  margin: 16px 0;
  text-align: center;
}

.OrderSummary_freeShippingNote__e05vI p {
  font-size: 12px;
  color: var(--cart-success);
  margin: 0;
  font-weight: 500;
}

.OrderSummary_totalRow__Y1Atx {
  display: flex;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 600;
  color: var(--cart-text-primary);
  padding-top: 16px;
  border-top: 2px solid var(--cart-border);
  margin-top: 16px;
}

.OrderSummary_securityNotice__UeMK_ {
  display: flex;
  align-items: center;
  gap: 12px;
  background: var(--background-light);
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.OrderSummary_securityIcon__43hD2 {
  font-size: 20px;
  flex-shrink: 0;
}

.OrderSummary_securityText__SPC9C {
  flex: 1;
}

.OrderSummary_securityText__SPC9C p {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}

.OrderSummary_securityText__SPC9C p:first-child {
  font-weight: 600;
  color: var(--cart-text-primary);
  margin-bottom: 2px;
}

.OrderSummary_securityText__SPC9C p:last-child {
  color: var(--cart-text-secondary);
}

.OrderSummary_policies___Ip9v {
  text-align: center;
}

.OrderSummary_policyText__xAK_B {
  font-size: 11px;
  color: var(--cart-text-muted);
  line-height: 1.4;
  margin: 0;
}

.OrderSummary_policyLink__cGL37 {
  color: var(--primary-color);
  text-decoration: none;
}

.OrderSummary_policyLink__cGL37:hover {
  text-decoration: underline;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .OrderSummary_item__1_k6i {
    gap: 10px;
    padding: 12px 0;
  }
  
  .OrderSummary_itemImage__a51EY {
    width: 50px;
    height: 50px;
  }
  
  .OrderSummary_itemName__qLipO {
    font-size: 13px;
  }
  
  .OrderSummary_itemCategory__bJ_6G {
    font-size: 11px;
  }
  
  .OrderSummary_itemPricing__VYAXu {
    font-size: 11px;
  }
  
  .OrderSummary_itemTotal__tr7yO {
    font-size: 13px;
  }
  
  .OrderSummary_pricingRow__Sju6_ {
    font-size: 13px;
  }
  
  .OrderSummary_totalRow__Y1Atx {
    font-size: 16px;
  }
  
  .OrderSummary_securityNotice__UeMK_ {
    padding: 12px;
  }
  
  .OrderSummary_securityText__SPC9C p {
    font-size: 11px;
  }
  
  .OrderSummary_policyText__xAK_B {
    font-size: 10px;
  }
}

/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/checkout/page.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
.page_container__ghkWP {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page_main__GwBeG {
  flex: 1;
  padding-top: 120px;
  padding-bottom: 40px;
  background: var(--background-light);
}

.page_checkoutContainer__bhZZB {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page_header__2eP1u {
  text-align: center;
  margin-bottom: 40px;
}

.page_title__c_7Td {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--cart-text-primary);
  margin: 0 0 12px 0;
}

.page_subtitle__K3glK {
  font-size: 1.1rem;
  color: var(--cart-text-secondary);
  margin: 0;
}

.page_checkoutContent__04zJN {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
  align-items: start;
}

.page_formSection__zZC9P {
  background: var(--cart-bg);
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 20px var(--cart-shadow);
}

.page_summarySection__zIzxB {
  background: var(--cart-bg);
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 20px var(--cart-shadow);
  position: sticky;
  top: 140px;
}

.page_loading__pcHNZ {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 20px;
}

.page_spinner__AJ4FH {
  width: 40px;
  height: 40px;
  border: 4px solid var(--cart-border);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: page_spin__z3RLY 1s linear infinite;
}

.page_loading__pcHNZ p {
  color: var(--cart-text-secondary);
  font-size: 1.1rem;
}

@keyframes page_spin__z3RLY {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile responsive */
@media (max-width: 1024px) {
  .page_checkoutContent__04zJN {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .page_summarySection__zIzxB {
    position: static;
    order: -1;
  }
}

@media (max-width: 768px) {
  .page_main__GwBeG {
    padding-top: 100px;
    padding-bottom: 20px;
  }
  
  .page_checkoutContainer__bhZZB {
    padding: 0 16px;
  }
  
  .page_header__2eP1u {
    margin-bottom: 30px;
  }
  
  .page_title__c_7Td {
    font-size: 2rem;
  }
  
  .page_subtitle__K3glK {
    font-size: 1rem;
  }
  
  .page_formSection__zZC9P,
  .page_summarySection__zIzxB {
    padding: 24px;
  }
  
  .page_checkoutContent__04zJN {
    gap: 20px;
  }
}

