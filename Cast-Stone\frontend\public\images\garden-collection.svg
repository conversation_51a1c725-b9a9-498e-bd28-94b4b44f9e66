<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="collection2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#228B22;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#D2B48C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B4513;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#collection2)"/>
  <circle cx="200" cy="150" r="60" fill="rgba(255,255,255,0.2)"/>
  <text x="200" y="140" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="white">Garden</text>
  <text x="200" y="165" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="white">Collection</text>
</svg>
