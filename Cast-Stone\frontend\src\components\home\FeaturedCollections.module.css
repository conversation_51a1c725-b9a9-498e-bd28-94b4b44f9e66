/* Featured Collections - Goop Style */
.featuredCollections {
  padding: 4rem 2rem;
  background: white;
}

.featuredGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

.featuredCard {
  position: relative;
  aspect-ratio: 3/4;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.featuredCard:hover {
  transform: translateY(-5px);
}

.featuredImage {
  position: relative;
  width: 100%;
  height: 70%;
}

.featuredImg {
  object-fit: cover;
  transition: transform 0.3s ease;
}

.featuredCard:hover .featuredImg {
  transform: scale(1.05);
}

.featuredContent {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 1.5rem;
  height: 30%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.featuredTitle {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.featuredSubtitle {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-bottom: 1rem;
  line-height: 1.3;
}

.featuredBtn {
  padding: 0.5rem 1rem;
  background: transparent;
  color: var(--text-dark);
  border: 1px solid var(--text-dark);
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: center;
}

.featuredBtn:hover {
  background: var(--text-dark);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .featuredGrid {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .featuredCard {
    aspect-ratio: 1/1.2;
  }

  .featuredContent {
    padding: 1rem;
  }

  .featuredTitle {
    font-size: 0.8rem;
  }

  .featuredSubtitle {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .featuredCollections {
    padding: 2rem 1rem;
  }

  .featuredGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .featuredCard {
    aspect-ratio: 4/3;
  }

  .featuredContent {
    position: relative;
    height: auto;
    padding: 1.5rem;
  }

  .featuredImage {
    height: 200px;
  }
}
