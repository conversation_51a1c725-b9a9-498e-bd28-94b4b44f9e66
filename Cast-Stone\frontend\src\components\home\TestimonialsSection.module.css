/* Testimonials Section */
.testimonialsSection {
  padding: 6rem 2rem;
  background: var(--background-light);
}

.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionHeader h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.sectionHeader p {
  font-size: 1.1rem;
  color: var(--text-light);
}

.testimonialsCarousel {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
}

.testimonialSlide {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.testimonialSlide.active {
  display: block;
  opacity: 1;
}

.testimonialCard {
  background: white;
  padding: 3rem;
  border-radius: 15px;
  box-shadow: var(--shadow);
  text-align: center;
}

.stars {
  margin-bottom: 1.5rem;
}

.star {
  color: #ffd700;
  font-size: 1.5rem;
  margin: 0 0.1rem;
}

.testimonialText {
  font-size: 1.2rem;
  font-style: italic;
  color: var(--text-dark);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.testimonialAuthor h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.testimonialAuthor span {
  color: var(--text-light);
  font-size: 0.9rem;
}

.testimonialControls {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.testimonialDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(139, 69, 19, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.testimonialDot.activeDot {
  background: var(--primary-color);
  transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .sectionHeader {
    margin-bottom: 2rem;
  }

  .testimonialCard {
    padding: 2rem;
  }

  .testimonialText {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .testimonialCard {
    padding: 1.5rem;
  }
}
